upstream api_server {
  server 127.0.0.1:48080;
}

server {
  listen 80;
  server_name localhost;
  location /api/ {
    proxy_pass http://api_server/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header REMOTE-HOST $remote_addr;
    client_max_body_size 20m;
  }

  # location /live/ {
  #   proxy_redirect off;
  #   # proxy_pass https://127.0.0.1:8084/;
  #   proxy_pass  https://***********:8084/;
  #   proxy_read_timeout 300;
  #   proxy_send_timeout 300;
  #   proxy_connect_timeout 60;
  #   proxy_set_header Host $host;
  #   proxy_set_header X-Real-IP $remote_addr;
  #   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  #   proxy_buffer_size 4k;
  #   # ssl_handshake_timeout 15s;
  #   proxy_http_version 1.1;
  #   proxy_set_header Upgrade $http_upgrade;
  #   proxy_set_header Connection "upgrade";
  # }

  location / {
    root /app/www;
    try_files $uri $uri/ /index.html;
    if ($request_filename ~* .*\.(?:htm|html)$) {
      add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
    }
  }
}
