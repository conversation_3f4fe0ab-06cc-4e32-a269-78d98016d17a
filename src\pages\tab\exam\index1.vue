<template>
  <view class="exam-box py-36rpx bg-white" v-if="Object.keys(paperInfo).length > 0">
    <view class="px-30rpx">
      <view>
        <text class="quesiton-type">{{
          questionType[paperInfo.questionTypeCode]
        }}</text>
        <text class="text-32rpx text-#333">{{ paperInfo.sequenceNum }}、{{ paperInfo.stem }}</text>
      </view>
      <view class="pl-30rpx">
        <!--  :disabled="pageType === 'detail'" -->
        <up-radio-group class="quesition-group" v-model="paperInfo.answerExercisesQuestion" labelSize="32rpx"
          labelColor="#333" placement="column" :class="pageType === 'detail'&& 'not-working'" v-if="
            paperInfo.questionTypeCode === 'B' ||
            paperInfo.questionTypeCode === 'C'
          ">
          <view v-for="(item, index) in paperInfo.checkOption" :key="index.toString().slice(-1)">
            <up-radio :customStyle="{ marginTop: '48rpx' }" :label="index.toString().slice(-1) + '、' + item"
              :name="index.toString().slice(-1)" :activeColor="resultBC(index.toString().slice(-1))"
              :labelColor="resultBC(index.toString().slice(-1))" v-if="item">
            </up-radio>
          </view>
        </up-radio-group>
        {{paperInfo.answerExercisesQuestion}}
        <up-checkbox-group class="quesition-group" v-model="paperInfo.answerExercisesQuestion" placement="column"
          labelSize="32rpx" labelColor="#333" :class="pageType === 'detail'&& 'not-working'"
          v-if="paperInfo.questionTypeCode === 'D'">
          <block v-for="(item, index) in paperInfo.checkOption" :key="index.toString().slice(-1)">
            <up-checkbox :customStyle="{ marginTop: '48rpx' }" :label="index.toString().slice(-1) + '、' + item"
              :name="index.toString().slice(-1)" :activeColor="resultD(index.toString().slice(-1))"
              :labelColor="resultD(index.toString().slice(-1))" v-if="item">
            </up-checkbox>
          </block>
        </up-checkbox-group>
      </view>
      <view v-if="pageType === 'detail'" class="mt-85rpx text-#333 text-28rpx">
        <view class="text-align-center flex items-center justify-around mb-48rpx"><up-line color="#EEEEEE"
            length="30%"></up-line><text>试题解析</text><up-line color="#EEEEEE" length="30%"></up-line></view>
        <view class="left-border">正确答案<text class="ml-20rpx text-#4076F6">{{
            paperInfo.questionTypeCode === "D"
              ? paperInfo.paperAnswer.split("").join("、")
              : paperInfo.paperAnswer
          }}</text></view>
        <view class="left-border my-48rpx">你的答案<text :class="[
              'ml-20rpx',
              paperInfo.answerStatus == 30 ? 'text-#FE474A' : 'text-#4076F6',
            ]">{{
              paperInfo.questionTypeCode === "D"
                ? paperInfo.answerExercisesQuestion.length
                  ? paperInfo.answerExercisesQuestion.join("、")
                  : ""
                : paperInfo.answerExercisesQuestion
            }}</text></view>
        <view class="left-border text-#999">试题解析
          <view class="mt-16rpx text-#333">{{
            paperInfo.paperQuestionAnalysis
          }}</view>
        </view>
      </view>
    </view>

    <view class="flex justify-between border-t-1px border-t-solid border-t-#E5E5E5 pt-20rpx px-30rpx"
      v-if="paperInfo && paperInfo.paperItemPointInfo">
      <view class="flex items-center">
        <view class="text-36rpx"><text class="text-#333">{{ paperInfo.sequenceNum }}</text><text
            class="text-#999">/{{ paperInfo.paperItemPointInfo.questionTotal }}</text></view>
        <view class="mx-40rpx">
          <up-icon name="order" label="答题卡" size="44rpx" labelPos="bottom" labelSize="24rpx" labelColor="#999"
            @click="goAnswerCard"></up-icon>
        </view>
        <!--  <up-icon name="file-text" label="交卷" size="44rpx" labelPos="bottom" labelSize="24rpx" labelColor="#999"
          @click="submitPaper"></up-icon> -->
        <up-icon v-if="pageType != 'detail'" name="file-text" label="交卷" size="44rpx" labelPos="bottom"
          labelSize="24rpx" labelColor="#999" @click="checkPaperAnswerAll('submit')"></up-icon>


      </view>
      <view class="flex items-center">
        <up-button type="primary" text="上一题" shape="circle" plain :customStyle="{
            marginRight: '20rpx',
            width: '146rpx',
            height: '54rpx',
          }" @click="
            getNextQuestion(
              paperInfo.questionExercisesPaperStatisticsId,
              paperInfo.paperItemPointInfo.sequenceNum,
              10
            )
          " :disabled="paperInfo.paperItemPointInfo.sequenceNum === 1"></up-button>
        <up-button type="primary" text="下一题" shape="circle" :customStyle="{ width: '146rpx', height: '54rpx' }" @click="
            getNextQuestion(
              paperInfo.questionExercisesPaperStatisticsId,
              paperInfo.paperItemPointInfo.sequenceNum,
              20,
              paperInfo.id
            )
          ">
          <!--  :disabled="
            paperInfo.paperItemPointInfo.sequenceNum ===
            paperInfo.paperItemPointInfo.questionTotal
          " -->
        </up-button>
      </view>
    </view>
  </view>
  <up-popup :show="popupShow" mode="bottom" @close="popupClose" round="12rpx">
    <view class="pa-30rpx">
      <view class="border-b-1px border-b-solid border-b-#eeeeee pb-30rpx text-28rpx text-#333">共{{ itemAllTotal }}道试题
      </view>
      <view class="py-30rpx answer-card">
        <block v-for="(item, index) in order" :key="index">
          <view v-if="answerCardInfo[item] && answerCardInfo[item].length">
            <view class="text-#666 text-28rpx">{{ questionType[item]
              }}<text>（每题{{ answerCardTotal[item].itemEveryScore }}分，共{{
                  answerCardTotal[item].itemGroupByCodeTotal
                }}题）</text></view>
            <view class="number-box">
              <text v-for="(ite, ind) in answerCardInfo[item]" :class="[
                  'quesition-number',
                  ite.answerStatus == 10 ? '' : 'answer',
                  pageType == 'detail' && ite.answerStatus == 20
                    ? 'right'
                    : pageType == 'detail' && ite.answerStatus == 30
                    ? 'wrong'
                    : '',
                ]" :key="ind" @click="getPaperInfo(ite.id)">{{ ind + 1 }}</text>
            </view>
          </view>
        </block>
      </view>
    </view>
  </up-popup>
</template>

<script setup lang="ts">
  import { ExamApi } from "@/api";
  const exercisesPlanId = ref(); //计划id
  const paperId = ref(); //试卷id
  const paperInfo = ref<any>({});
  const pageType = ref("");
  const token = ref();
  const questionType : any = {
    B: "单选题",
    D: "多选题",
    C: "判断题",
    E: "简答题",
    F: "论述题",
  };
  const popupShow = ref(false);

  let resultBC = computed(() => (item) => {
    if (pageType.value === 'detail') {
      if (item == paperInfo.value.answerExercisesQuestion) {
        return paperInfo.value.answerExercisesQuestion === paperInfo.value.paperAnswer
          ? "#4076F6"
          : "#FE474A";
      }
    }
    return ""
  })


  let resultD = computed(() => (item) => {
    let color : any = ''
    if (pageType.value === 'detail') {
      if (paperInfo.value.answerExercisesQuestion?.includes(item)) {
        color = '#FE474A';
        let bol = paperInfo.value.paperAnswer?.split('').some(itemr => itemr == item)
        if (!!bol) {
          color = '#4076F6';
        }
      }
      return color
    }
    return ""
  })

  //初始 根据id获取题目明细
  const getPaperInfo = async (id) => {
    const params = {
      id: id,
    };
    const res = await ExamApi.findItemById(params);
    paperInfo.value = res.data;
    if (res.data.questionTypeCode === "D") {
      paperInfo.value.answerExercisesQuestion = [];
    }
    popupShow.value = false;
  };
  // 获取上一题下一题
  const getNextQuestion = async (
    questionExercisesPaperStatisticsId,
    sequenceNum,
    operateType,
    questionExercisesPaperItemId
  ) => {
    if (pageType.value == "detail") {
      getQuestion(questionExercisesPaperStatisticsId, sequenceNum, operateType);
    } else {
      if (operateType == 20 && questionExercisesPaperItemId) {
        //下一题之前提交答案
        const params = {
          questionExercisesPaperItemId: questionExercisesPaperItemId,
          answerExamQuestion:
            paperInfo.value.questionTypeCode === "D"
              ? paperInfo.value.answerExercisesQuestion.join(",")
              : paperInfo.value.answerExercisesQuestion,
        };
        const res = await ExamApi.checkStageSubmit(params);

        // 不是最后一题
        if (!res.data.paperItemPointInfo.lastFlag) {
          if (res.code === 0) {
            getQuestion(
              questionExercisesPaperStatisticsId,
              sequenceNum,
              operateType
            );
          }
        } else {
          console.log('是最后一题')
          checkPaperAnswerAll('next')
        }

      }
      if (operateType == 10) {
        getQuestion(questionExercisesPaperStatisticsId, sequenceNum, operateType);
      }
    }
  };
  //获取题目
  const getQuestion = async (
    questionExercisesPaperStatisticsId,
    sequenceNum,
    operateType
  ) => {
    uni.showLoading({
      title: "加载中",
    });
    const infoParams = {
      questionExercisesPaperStatisticsId: questionExercisesPaperStatisticsId,
      exercisesPlanId: exercisesPlanId.value,
      paperId: paperId.value,
      sequenceNum: sequenceNum,
      operateType: operateType,
    };
    const resInfo = await ExamApi.findNextItem(infoParams);
    console.log(resInfo, 'resInforesInfo')
    uni.hideLoading();
    paperInfo.value = resInfo.data;
    if (resInfo.data.questionTypeCode === "D") {
      if (pageType.value === 'detail') {
        paperInfo.value.answerExercisesQuestion = resInfo.data.answerExercisesQuestion.split(',');
      }
    }
  };
  //提交试卷
  const submitPaper = async () => {
    // uni.showModal({
    //   title: "提示",
    //   content: "是否确认交卷",
    //   success: async function (res) {
    //     if (res.confirm) {
    const params = {
      exercisesPlanId: exercisesPlanId.value,
      paperId: paperId.value,
      id: paperInfo.value.questionExercisesPaperStatisticsId,
      token: token.value,
    };
    const resSubmit = await ExamApi.submitPaper(params);
    if (resSubmit.code === 0) {
      uni.showToast({
        title: "交卷成功",
        icon: "success",
      });
      //uni.navigateBack();
      const queryInfo = {
        exercisesPaperScore: resSubmit.data.exercisesPaperScore,
        exercisesPaperComment: resSubmit.data.exercisesPaperComment,
      };
      uni.navigateTo({
        url: `/pages/tab/submit/index?queryInfo=${JSON.stringify(queryInfo)}`,
      });
    }
    //     }
    //   },
    // });
  };
  const order = ["B", "D", "C", "E", "F"];
  const answerCardInfo = ref<any>({});
  const itemAllTotal = ref();
  const answerCardTotal = ref<any>({});
  //答题卡
  const goAnswerCard = async () => {
    const params = {
      questionExercisesPaperStatisticsId:
        paperInfo.value.questionExercisesPaperStatisticsId,
      exercisesPlanId: exercisesPlanId.value,
      paperId: paperId.value,
      sequenceNum: paperInfo.value.paperItemPointInfo.sequenceNum,
    };
    const res = await ExamApi.queryAnswerCardItem(params);
    popupShow.value = true;
    itemAllTotal.value = res.data.itemAllTotal;
    answerCardInfo.value = res.data.answerCardInfo;
    answerCardTotal.value = res.data.answerCardTotal;
  };

  const popupClose = () => {
    popupShow.value = false;
  };


  const checkPaperAnswerAll = async (form) => {
    const { data } = await ExamApi.checkPaperAnswerAll({ paperStatisticsId: paperInfo.value.questionExercisesPaperStatisticsId });

    if (form == 'next' && !data) {
      return uni.showToast({
        title: "已无下一题",
        icon: "none",
        duration: 2000,
      });
    }

    let msg = '';
    if (form == 'next') {
      msg = '当前测试卷所有试题已答完，是否交卷?'
    } else if (form == 'submit') {
      msg = !!data ? '交卷后此次模拟结束，是否确认提交试卷?' : '当前模拟卷仍有试题未答完，是否确认提交试卷?'
    }

    return uni.showModal({
      title: '提示', // 模态框标题
      content: msg, // 模态框内容
      showCancel: true, // 是否显示取消按钮，默认为true
      cancelText: '取消', // 取消按钮的文字
      confirmText: '确定', // 确定按钮的文字
      success(res) {
        if (res.confirm) {
          submitPaper()
          // 执行确认后的逻辑
        } else if (res.cancel) {
          // 执行取消后的逻辑
        }
      },
    });
  }

  onMounted(() => {
    const { proxy } = getCurrentInstance();
    const options = proxy.options || proxy.$scope.options;
    uni.setNavigationBarTitle({
      title: options.paperName,
    });
    exercisesPlanId.value = options.exercisesPlanId;
    paperId.value = options.paperId;
    if (options.pageType == "detail") {
      //答题详情
      pageType.value = options.pageType;
      getNextQuestion(
        options.questionExercisesPaperStatisticsId,
        options.sequenceNum,
        20
      );
      // getPaperInfo(options.lastPaperItemId);
    } else {
      //测试答题
      token.value = options.token;
      const ponitInfo = JSON.parse(options.paperItemPointInfo);
      if (ponitInfo.lastPaperItemId) {
        getPaperInfo(ponitInfo.lastPaperItemId);
      } else {
        // 获取第一题
        getNextQuestion(
          ponitInfo.questionExercisesPaperStatisticsId,
          ponitInfo.sequenceNum,
          20
        );
      }
    }
  });
</script>

<style scoped lang="scss">
  .exam-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: calc(100vh - 44px);
    box-sizing: border-box;

    .quesiton-type {
      display: inline-block;
      width: 92rpx;
      height: 38rpx;
      line-height: 38rpx;
      background: url("/static/images/mocktest/type-bg.png") no-repeat;
      background-size: 100% 100%;
      font-size: 20rpx;
      padding-left: 16rpx;
    }
  }

  .answer-card {
    max-height: 60vh;
    overflow-y: scroll;

    .number-box {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 20rpx 40rpx;
      align-content: center;
      justify-content: center;
      margin: 30rpx 0;

      .quesition-number {
        width: 66rpx;
        height: 66rpx;
        border-radius: 50%;
        border: 1rpx solid #e2e2e2;
        text-align: center;
        line-height: 66rpx;
      }

      .answer,
      .right {
        background: #f5f8ff;
        border: 1rpx solid #4076f6;
        color: #4076f6;
      }

      .wrong {
        background: rgba(254, 71, 74, 0.08);
        border: 1rpx solid #fe474a;
        color: #fe474a;
      }
    }
  }

  .quesition-group {
    .u-radio {
      display: flex;
      align-items: flex-start;

      :deep(uni-text.u-radio__text) {
        flex: 1;
      }
    }
  }


  .left-border {
    position: relative;

    &::before {
      content: "";
      position: absolute;
      left: -30rpx;
      top: 6rpx;
      width: 8rpx;
      height: 28rpx;
      background: linear-gradient(180deg, #74aefb 0%, #4076f6 100%);
      border-radius: 0rpx 6rpx 6rpx 0rpx;
    }
  }



  .not-working {
    user-select: none;
    pointer-events: none;
  }
</style>
