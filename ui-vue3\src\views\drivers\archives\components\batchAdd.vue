<template>
  <div class="batch-add-container">
    <div class="batch-add-content">
      <!-- 第一步：下载导入模板 -->
      <div class="step-item">
        <div class="step-header">
          <span class="step-number">第一步：</span>
          <span class="step-title">下载导入模板</span>
        </div>
        <div class="step-description">
          根据模板格式填写信息完善表格内容
        </div>
        <div class="step-action">
          <a-button type="primary" @click="downloadTemplate">
            下载模板
          </a-button>
        </div>
      </div>

      <!-- 第二步：导入完善后的数据 -->
      <div class="step-item">
        <div class="step-header">
          <span class="step-number">第二步：</span>
          <span class="step-title">导入完善后的数据</span>
        </div>
        <div class="step-description">
          支持格式：xlsx、xls
        </div>
        <div class="step-action">
          <a-upload
            :before-upload="beforeUpload"
            :show-upload-list="false"
            accept=".xlsx,.xls"
          >
            <a-button type="primary">
              导入数据
            </a-button>
          </a-upload>
        </div>
      </div>

      <!-- 上传状态显示 -->
      <div v-if="uploadedFile" class="upload-status">
        <a-alert
          :message="`已选择文件: ${uploadedFile.name}`"
          type="success"
          show-icon
          closable
          @close="clearUploadedFile"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { downloadTemplate as downloadTemplateApi, batchImport as batchImportApi } from '../list.api';

const emit = defineEmits(['success', 'cancel']);

const uploadedFile = ref<File | null>(null);

// 下载模板
const downloadTemplate = async () => {
  try {
    message.loading('正在下载模板...', 0);

    // 调用API下载模板
    const response = await downloadTemplateApi();

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = '司机档案导入模板.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    message.destroy();
    message.success('模板下载成功！');
  } catch (error) {
    message.destroy();
    message.error('模板下载失败！');
    console.error('下载模板失败:', error);
  }
};

// 上传前验证和处理
const beforeUpload = async (file: File) => {
  // 验证文件类型
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel' ||
                  file.name.toLowerCase().endsWith('.xlsx') ||
                  file.name.toLowerCase().endsWith('.xls');

  if (!isExcel) {
    message.error('只能上传Excel文件！');
    return false;
  }

  // 验证文件大小（限制10MB）
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB！');
    return false;
  }

  try {
    message.loading('正在导入数据...', 0);

    // 调用批量导入API
    const response = await batchImportApi(file);

    message.destroy();

    if (response && response.success) {
      uploadedFile.value = file;
      message.success(`文件 ${file.name} 导入成功！`);

      // 触发成功事件
      emit('success', file);
    } else {
      message.error(response?.message || '导入失败！');
    }
  } catch (error) {
    message.destroy();
    message.error('导入失败！');
    console.error('批量导入失败:', error);
  }

  return false; // 阻止自动上传
};

// 清除上传的文件
const clearUploadedFile = () => {
  uploadedFile.value = null;
};

// 确认操作
const handleConfirm = () => {
  if (!uploadedFile.value) {
    message.warning('请先上传数据文件！');
    return;
  }

  emit('success', uploadedFile.value);
  clearUploadedFile();
};

// 取消操作
const handleCancel = () => {
  clearUploadedFile();
  emit('cancel');
};
</script>

<style lang="less" scoped>
.batch-add-container {
  .batch-add-content {
    padding: 20px;

    .step-item {
      margin-bottom: 32px;

      .step-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .step-number {
          font-weight: 500;
          color: #333;
        }

        .step-title {
          font-weight: 500;
          color: #333;
        }
      }

      .step-description {
        color: #666;
        font-size: 14px;
        margin-bottom: 16px;
        margin-left: 0;
      }

      .step-action {
        display: flex;
        justify-content: flex-end;
      }
    }

    .footer-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
      margin-top: 32px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
    }
  }
}
</style>

