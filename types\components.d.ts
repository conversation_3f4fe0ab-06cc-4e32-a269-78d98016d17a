/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AgreePrivacy: typeof import('./../src/components/agree-privacy/index.vue')['default']
    BaseWrapper: typeof import('./../src/components/base-wrapper/base-wrapper.vue')['default']
    DaTree: typeof import('./../src/components/da-tree/index.vue')['default']
    GTabBar: typeof import('./../src/components/g-tab-bar/g-tab-bar.vue')['default']
    NoData: typeof import('./../src/components/no-data/no-data.vue')['default']
    PageNav: typeof import('./../src/components/page-nav/page-nav.vue')['default']
    ProgressCircle: typeof import('./../src/components/progress-circle/index.vue')['default']
  }
}
