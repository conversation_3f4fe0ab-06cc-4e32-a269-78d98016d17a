<template>
  <JFormContainer :disabled="formDisabled">
    <template #detail>
    <a-form ref="formRef" :model="formData" :rules="formRules" :labelCol="labelCol" :wrapperCol="wrapperCol" :disabled="formDisabled">
      <a-divider orientation="left">基础信息</a-divider>
      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item label="姓名" name="name">
            <a-input v-model:value="formData.name" placeholder="请输入姓名" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="证件类型" name="idType">
            <a-select v-model:value="formData.idType" placeholder="请选择证件类型">
              <a-select-option v-for="item in idTypeOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="证件号码" name="idNumber">
            <a-input v-model:value="formData.idNumber" placeholder="请输入证件号码" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item label="证件有效期" name="idValidate">
            <a-range-picker v-model:value="formData.idValidate" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="手机号" name="phone">
            <a-input v-model:value="formData.phone" placeholder="请输入手机号" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="民族" name="nation">
            <a-select v-model:value="formData.nation" placeholder="请选择民族">
              <a-select-option v-for="item in nationOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item label="性别" name="gender">
            <a-select v-model:value="formData.gender" placeholder="请选择性别">
              <a-select-option v-for="item in genderOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="出生年月" name="birthDate">
            <a-date-picker v-model:value="formData.birthDate" placeholder="请选择出生年月" picker="month" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="年龄" name="age">
            <a-input-number v-model:value="formData.age" placeholder="请输入年龄" :min="0" :max="120" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item label="学历" name="education">
            <a-select v-model:value="formData.education" placeholder="请选择学历">
              <a-select-option v-for="item in educationOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="政治面貌" name="politicalStatus">
            <a-select v-model:value="formData.politicalStatus" placeholder="请选择政治面貌">
              <a-select-option v-for="item in politicalStatusOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="所在城市" name="city">
            <a-input v-model:value="formData.city" placeholder="请输入所在城市" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item label="客户单位" name="company">
            <a-input v-model:value="formData.company" placeholder="请输入客户单位" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="家庭住址" name="address">
            <a-input v-model:value="formData.address" placeholder="请输入家庭住址" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="有无纹身" name="hasTattoo">
            <a-select v-model:value="formData.hasTattoo" placeholder="请选择">
              <a-select-option v-for="item in yesNoOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item label="合同类型" name="contractType">
            <a-select v-model:value="formData.contractType" placeholder="请选择合同类型">
              <a-select-option v-for="item in contractTypeOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="入职时间" name="entryDate">
            <a-date-picker v-model:value="formData.entryDate" placeholder="请选择入职时间" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="离职时间" name="leaveDate">
            <a-date-picker v-model:value="formData.leaveDate" placeholder="请选择离职时间" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-divider orientation="left">紧急联系人</a-divider>
      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item label="紧急联系人" name="emergencyContact">
            <a-input v-model:value="formData.emergencyContact" placeholder="请输入紧急联系人" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="紧急联系电话" name="emergencyPhone">
            <a-input v-model:value="formData.emergencyPhone" placeholder="请输入紧急联系电话" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-divider orientation="left">教育经历</a-divider>
      <div class="ma-4">
        <a-button type="primary" @click="addEducation" style="margin-bottom: 16px">
          <template #icon><PlusOutlined /></template>
          添加教育经历
        </a-button>
        <a-table :columns="educationColumns" :data-source="formData.educationList" :pagination="false" bordered size="small">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'timeRange'">
              <a-input v-model:value="record.timeRange" placeholder="请输入时间范围" />
            </template>
            <template v-else-if="column.key === 'educationLevel'">
              <a-input v-model:value="record.educationLevel" placeholder="请输入教育层次" />
            </template>
            <template v-else-if="column.key === 'school'">
              <a-input v-model:value="record.school" placeholder="请输入毕业院校" />
            </template>
            <template v-else-if="column.key === 'major'">
              <a-input v-model:value="record.major" placeholder="请输入所学专业" />
            </template>
            <template v-else-if="column.key === 'action'">
              <a-button type="link" danger @click="removeEducation(index)">删除</a-button>
            </template>
          </template>
        </a-table>
      </div>

      <a-divider orientation="left">技能相关</a-divider>
      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item label="驾驶证类型" name="driverLicenseType">
            <a-select v-model:value="formData.driverLicenseType" placeholder="请选择驾驶证类型">
              <a-select-option v-for="item in driverLicenseTypeOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="驾驶证有效期" name="driverLicenseExpiry">
            <a-range-picker v-model:value="formData.driverLicenseExpiry" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="工作经验" name="workExperience">
            <a-select v-model:value="formData.workExperience" placeholder="请选择工作经验">
              <a-select-option v-for="item in workExperienceOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item label="办公技能" name="officeSkills">
            <a-select v-model:value="formData.officeSkills" placeholder="请选择办公技能">
              <a-select-option v-for="item in officeSkillsOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="英文水平" name="englishLevel">
            <a-select v-model:value="formData.englishLevel" placeholder="请选择英文水平">
              <a-select-option v-for="item in englishLevelOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="车辆安全检查与维修" name="vehicleMaintenance" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
            <a-select v-model:value="formData.vehicleMaintenance" placeholder="请选择">
              <a-select-option v-for="item in skillLevelOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item label="是否有高端汽车驾驶经验" name="dangerousVehicleExperience" :label-col="{ span: 10 }" :wrapper-col="{ span: 12 }">
            <a-select v-model:value="formData.dangerousVehicleExperience" placeholder="请选择">
              <a-select-option value="是">是</a-select-option>
              <a-select-option value="否">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="是否具有基础急救资格证书" name="heavyVehicleCertificate" :label-col="{ span: 10 }" :wrapper-col="{ span: 12 }">
            <a-select v-model:value="formData.heavyVehicleCertificate" placeholder="请选择">
              <a-select-option value="是">是</a-select-option>
              <a-select-option value="否">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="是否有特种驾驶技能培训且合格" name="dangerousGoodsTraining" :label-col="{ span: 12 }" :wrapper-col="{ span: 10 }">
            <a-select v-model:value="formData.dangerousGoodsTraining" placeholder="请选择">
              <a-select-option value="是">是</a-select-option>
              <a-select-option value="否">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-divider orientation="left">工作经历</a-divider>
      <div class="ma-4">
        <a-button type="primary" @click="addWorkExperience" style="margin-bottom: 16px">
          <template #icon><PlusOutlined /></template>
          添加工作经历
        </a-button>
        <a-table :columns="workExperienceColumns" :data-source="formData.workExperienceList" :pagination="false" bordered size="small">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'entryTime'">
              <a-input v-model:value="record.entryTime" placeholder="请输入入职时间" />
            </template>
            <template v-else-if="column.key === 'leaveTime'">
              <a-input v-model:value="record.leaveTime" placeholder="请输入离职时间" />
            </template>
            <template v-else-if="column.key === 'company'">
              <a-input v-model:value="record.company" placeholder="请输入所在单位" />
            </template>
            <template v-else-if="column.key === 'position'">
              <a-input v-model:value="record.position" placeholder="请输入担任职位" />
            </template>
            <template v-else-if="column.key === 'action'">
              <a-button type="link" danger @click="removeWorkExperience(index)">删除</a-button>
            </template>
          </template>
        </a-table>
      </div>
      <a-divider orientation="left">文件上传（九项资料）</a-divider>

      <!-- 使用栅格布局，每一项占一行 -->
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="应聘登记表" name="applicationForm" :label-col="{ span: 3 }">
            <FileUploadItem v-model:fileList="formData.applicationForm" :disabled="formDisabled" title="应聘登记表" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="入职登记表" name="entryForm" :label-col="{ span: 3 }">
            <FileUploadItem v-model:fileList="formData.entryForm" :disabled="formDisabled" title="入职登记表" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="推荐登记表" name="recommendationForm" :label-col="{ span: 3 }">
            <FileUploadItem v-model:fileList="formData.recommendationForm" :disabled="formDisabled" title="推荐登记表" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="路试测评表" name="roadTestForm" :label-col="{ span: 3 }">
            <FileUploadItem v-model:fileList="formData.roadTestForm" :disabled="formDisabled" title="路试测评表" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="胜任力模型测评报告" name="competencyReport" :label-col="{ span: 3 }">
            <FileUploadItem v-model:fileList="formData.competencyReport" :disabled="formDisabled" title="胜任力模型测评报告" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="心理测评报告" name="psychologyReport" :label-col="{ span: 3 }">
            <FileUploadItem v-model:fileList="formData.psychologyReport" :disabled="formDisabled" title="心理测评报告" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="体检报告" name="medicalReport" :label-col="{ span: 3 }">
            <FileUploadItem v-model:fileList="formData.medicalReport" :disabled="formDisabled" title="体检报告" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="有无犯罪记录" name="criminalRecord" :label-col="{ span: 3 }">
            <FileUploadItem v-model:fileList="formData.criminalRecord" :disabled="formDisabled" title="有无犯罪记录" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-item label="家访记录表" name="homeVisitRecord" :label-col="{ span: 3 }">
            <FileUploadItem v-model:fileList="formData.homeVisitRecord" :disabled="formDisabled" title="家访记录表" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-divider orientation="left">其他</a-divider>

      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="formData.remark" placeholder="请输入备注" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </JFormContainer>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import FileUploadItem from './FileUploadItem.vue';
  const props = defineProps({
    formDisabled: {
      type: Boolean,
      default: '',
    },
  });
  const formRef = ref();
  const formData = reactive({
    //基础信息
    name: undefined,
    idType: undefined,
    idNumber: undefined,
    idValidate: undefined,
    phone: undefined,
    nation: undefined,
    gender: undefined,
    birthDate: undefined,
    age: null,
    education: undefined,
    politicalStatus: undefined,
    city: undefined,
    company: undefined,
    address: undefined,
    hasTattoo: undefined,
    contractType: undefined,
    entryDate: undefined,
    leaveDate: undefined,
    //紧急联系人
    emergencyContact: undefined,
    emergencyPhone: undefined,
    //技能相关
    driverLicenseType: undefined,
    driverLicenseExpiry: undefined,
    workExperience: undefined,
    officeSkills: undefined,
    englishLevel: undefined,
    vehicleMaintenance: undefined,
    dangerousVehicleExperience: undefined,
    heavyVehicleCertificate: undefined,
    dangerousGoodsTraining: undefined,
    //教育经历
    educationList: [] as Array<{
      timeRange: string;
      educationLevel: string;
      school: string;
      major: string;
    }>,
    //工作经历
    workExperienceList: [] as Array<{
      entryTime: string;
      leaveTime: string;
      company: string;
      position: string;
    }>,
    //九项资料文件上传
    applicationForm: [], // 应聘登记表
    entryForm: [], // 入职登记表
    recommendationForm: [], // 推荐登记表
    roadTestForm: [], // 路试测评表
    competencyReport: [], // 胜任力模型测评报告
    psychologyReport: [], // 心理测评报告
    medicalReport: [], // 体检报告
    criminalRecord: [], // 有无犯罪记录
    homeVisitRecord: [], // 家访记录表
    remark: undefined,
  });

  const labelCol = reactive({ xs: { span: 24 }, sm: { span: 6 } });
  const wrapperCol = reactive({ xs: { span: 24 }, sm: { span: 16 } });
  // 证件类型选项
  const idTypeOptions = reactive([
    { label: '居民身份证', value: '1' },
    { label: '中国护照', value: '2' },
    { label: '港澳居民身份证', value: '3' },
    { label: '港澳居民来内地通行证', value: '4' },
  ]);

  // 民族选项
  const nationOptions = reactive([
    { label: '汉族', value: '01' },
    { label: '蒙古族', value: '02' },
    { label: '回族', value: '03' },
    { label: '藏族', value: '04' },
    { label: '维吾尔族', value: '05' },
  ]);

  // 性别选项
  const genderOptions = reactive([
    { label: '男', value: '1' },
    { label: '女', value: '2' },
  ]);

  // 学历选项
  const educationOptions = reactive([
    { label: '小学', value: '1' },
    { label: '初中', value: '2' },
    { label: '高中', value: '3' },
    { label: '中专', value: '4' },
    { label: '大专', value: '5' },
    { label: '本科', value: '6' },
    { label: '硕士', value: '7' },
    { label: '博士', value: '8' },
  ]);

  // 政治面貌选项
  const politicalStatusOptions = reactive([
    { label: '中共党员', value: '01' },
    { label: '中共预备党员', value: '02' },
    { label: '共青团员', value: '03' },
    { label: '民革党员', value: '04' },
    { label: '民盟盟员', value: '05' },
    { label: '民建会员', value: '06' },
    { label: '民进会员', value: '07' },
    { label: '农工党党员', value: '08' },
    { label: '致公党党员', value: '09' },
    { label: '九三学社社员', value: '10' },
    { label: '台盟盟员', value: '11' },
    { label: '无党派人士', value: '12' },
    { label: '群众', value: '13' },
  ]);

  // 是否选项
  const yesNoOptions = reactive([
    { label: '是', value: '1' },
    { label: '否', value: '0' },
  ]);

  // 合同类型选项
  const contractTypeOptions = reactive([
    { label: '正式合同', value: '1' },
    { label: '临时合同', value: '2' },
    { label: '劳务合同', value: '3' },
    { label: '实习合同', value: '4' },
  ]);

  // 驾驶证类型选项
  const driverLicenseTypeOptions = reactive([
    { label: 'C1', value: 'C1' },
    { label: 'C2', value: 'C2' },
    { label: 'B1', value: 'B1' },
    { label: 'B2', value: 'B2' },
    { label: 'A1', value: 'A1' },
    { label: 'A2', value: 'A2' },
    { label: 'A3', value: 'A3' },
  ]);

  // 工作经验选项
  const workExperienceOptions = reactive([
    { label: '1年以下', value: '1年以下' },
    { label: '1-3年', value: '1-3年' },
    { label: '3-5年', value: '3-5年' },
    { label: '5-10年', value: '5-10年' },
    { label: '10年以上', value: '10年以上' },
  ]);

  // 办公技能选项
  const officeSkillsOptions = reactive([
    { label: '一般', value: '一般' },
    { label: '熟练', value: '熟练' },
    { label: '精通', value: '精通' },
  ]);

  // 英文水平选项
  const englishLevelOptions = reactive([
    { label: '一般', value: '一般' },
    { label: '良好', value: '良好' },
    { label: '熟练', value: '熟练' },
    { label: '精通', value: '精通' },
  ]);

  // 技能等级选项
  const skillLevelOptions = reactive([
    { label: '会', value: '会' },
    { label: '熟练', value: '熟练' },
    { label: '精通', value: '精通' },
  ]);

  // 教育经历表格列定义
  const educationColumns = reactive([
    { title: '时间范围', dataIndex: 'timeRange', key: 'timeRange', width: 200 },
    { title: '教育层次', dataIndex: 'educationLevel', key: 'educationLevel', width: 150 },
    { title: '毕业院校', dataIndex: 'school', key: 'school', width: 200 },
    { title: '所学专业', dataIndex: 'major', key: 'major', width: 200 },
    { title: '操作', key: 'action', width: 100 },
  ]);

  // 工作经历表格列定义
  const workExperienceColumns = reactive([
    { title: '入职时间', dataIndex: 'entryTime', key: 'entryTime', width: 150 },
    { title: '离职时间', dataIndex: 'leaveTime', key: 'leaveTime', width: 150 },
    { title: '所在单位', dataIndex: 'company', key: 'company', width: 200 },
    { title: '担任职位', dataIndex: 'position', key: 'position', width: 200 },
    { title: '操作', key: 'action', width: 100 },
  ]);

  // 添加教育经历
  const addEducation = () => {
    formData.educationList.push({
      timeRange: '',
      educationLevel: '',
      school: '',
      major: '',
    });
  };

  // 删除教育经历
  const removeEducation = (index: number) => {
    formData.educationList.splice(index, 1);
  };

  // 添加工作经历
  const addWorkExperience = () => {
    formData.workExperienceList.push({
      entryTime: '',
      leaveTime: '',
      company: '',
      position: '',
    });
  };

  // 删除工作经历
  const removeWorkExperience = (index: number) => {
    formData.workExperienceList.splice(index, 1);
  };

  const formRules = reactive({});

  // 导出供父组件使用
  defineExpose({
    formRef,
    formData,
    formRules,
  });
</script>
<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
