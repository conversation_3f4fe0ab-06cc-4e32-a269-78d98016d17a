<template>
  <view
    class="footer flex justify-between border-t-1px border-t-#E5E5E5 border-t-solid px-30rpx py-36rpx pt-20rpx"
  >
    <view class="flex items-center">
      <view class="text-36rpx">
        <text class="text-#333">
          {{ paperInfo.sequenceNum }}
        </text>
        <text class="text-#999">
          /{{ questionTotal }}
        </text>
      </view>
      <view v-if="props.paperType === paperType.brush" class="mx-40rpx">
        <up-icon
          :name="
            paperInfo.libraryItemPointInfo?.collectFlag ? 'star-fill' : 'star'
          "
          :label="
            paperInfo.libraryItemPointInfo?.collectFlag ? '取消收藏' : '收藏'
          "
          size="44rpx"
          label-pos="bottom"
          label-size="24rpx"
          label-color="#999"
          :color="paperInfo.libraryItemPointInfo?.collectFlag && 'orange'"
          @click="handleCollect"
        />
      </view>
      <view v-if="props.paperType === paperType.exam" class="mx-40rpx">
        <up-icon
          name="order"
          label="答题卡"
          size="44rpx"
          label-pos="bottom"
          label-size="24rpx"
          label-color="#999"
          @click="handleShowAnswerCard"
        />
      </view>
      <view
        v-if="
          props.paperType === paperType.exam
            && props.queryInfo?.requestType === 5
        "
      >
        <up-icon
          name="file-text"
          label="交卷"
          size="44rpx"
          label-pos="bottom"
          label-size="24rpx"
          label-color="#999"
          @click="handleClickSubmit"
        />
      </view>
    </view>
    <view class="flex items-center">
      <up-button
        type="primary"
        text="上一题"
        shape="circle"
        plain
        :custom-style="{
          marginRight: '20rpx',
          width: '146rpx',
          height: '54rpx',
        }"
        @click="handleClickUp"
      />
      <up-button
        type="primary"
        text="下一题"
        shape="circle"
        :custom-style="{ width: '146rpx', height: '54rpx' }"
        @click="handleClickDown"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { BrushApi, ExamApi } from '@/api';

const props = defineProps({
  paperInfo: {
    type: Object as () => any,
    requred: true,
  },
  queryInfo: {
    type: Object,
    required: true,
  },
  answer: {
    type: String,
    required: true,
  },
  paperType: {
    type: Number,
    required: true,
  },
});
const emits = defineEmits([
  'handleClickUp',
  'handleClickDown',
  'handleCollect',
  'handleShowAnswerCard',
  'handleClickSubmit',
]);
enum paperType {
  brush,
  exam,
}
const answerFormatter = computed(() => {
  if (Array.isArray(props.answer)) {
    return props.answer.join(',');
  }
  return props.answer;
});
const questionTotal = computed(() => {
  if (props.paperType === paperType.brush) {
    return props.paperInfo.libraryItemPointInfo?.questionTotal;
  }
  else {
    return props.paperInfo.paperItemPointInfo?.questionTotal;
  }
});
// 刷题模式start
const enterNewKonwledgePoint = async (lastItemId: number, treeId: number) => {
  console.log('进入新的知识树');
  uni.showLoading({
    title: '加载中',
  });
  const { exercisesPlanId, questionLibraryId, requestType } = props.queryInfo;
  if (lastItemId) {
    // 非首次答题,获取题目坐标
    const res = await BrushApi.findLibraryItemByRequestType({
      libraryItemId: lastItemId,
      requestType,
    });
    const { data } = await BrushApi.findNextLibraryItem({
      exercisesPlanId,
      questionLibraryId,
      requestType,
      parentKnowledgeTreeId: treeId,
      sequenceNum: res.data.sequenceNum,
      operateType: res.data.libraryItemPointInfo.lastFlag ? 30 : 20,
    });
    emits('handleClickDown', data);
    uni.hideLoading();
  }
  else {
    // 首次答题
    const { data } = await BrushApi.findNextLibraryItem({
      exercisesPlanId,
      questionLibraryId,
      requestType,
      sequenceNum: 0,
      operateType: 20,
      parentKnowledgeTreeId: treeId,
    });
    emits('handleClickDown', data);
    uni.hideLoading();
  }
};
const brushClickUp = async () => {
  const {
    exercisesPlanId,
    questionLibraryId,
    sequenceNum,
    parentKnowledgeTreeId,
    libraryItemPointInfo,
  } = props.paperInfo;
  const { requestType } = props.queryInfo;
  const isFirstQuestion = sequenceNum === 1;
  if (isFirstQuestion) {
    const { data } = await BrushApi.findAdjacentTree({
      exercisesPlanId,
      questionLibraryId,
      requestType,
      treeId: libraryItemPointInfo.parentKnowledgeTreeId,
      operateType: 10,
    });
    if (data) {
      const { lastItemId, treeId } = data;
      await enterNewKonwledgePoint(lastItemId, treeId);
    }
    else {
      uni.showToast({
        title: '当前题库已无上一题',
        icon: 'none',
        duration: 2000,
      });
    }
  }
  else {
    uni.showLoading({
      title: '加载中',
    });
    const { data } = await BrushApi.findNextLibraryItem({
      exercisesPlanId,
      questionLibraryId,
      requestType,
      sequenceNum,
      parentKnowledgeTreeId,
      operateType: 10,
    });
    emits('handleClickUp', data);
    uni.hideLoading();
  }
};
const brushClickDown = async () => {
  const {
    sequenceNum,
    id,
    answerStatus,
    libraryItemPointInfo,
    parentKnowledgeTreeId,
  } = props.paperInfo;
  const { exercisesPlanId, questionLibraryId, requestType } = props.queryInfo;
  const isNeedCheck = answerStatus === 10 && requestType !== 3;
  const isLastQuestion = libraryItemPointInfo.lastFlag;

  if (isNeedCheck) {
    const res = await BrushApi.checkLibraryStageSubmit({
      questionExercisesLibraryItemId: id,
      requestType,
      answerExamQuestion: answerFormatter.value,
    });
    if (res) {
      uni.showLoading();
      const { data } = await BrushApi.findNextLibraryItem({
        exercisesPlanId,
        questionLibraryId,
        requestType,
        sequenceNum,
        parentKnowledgeTreeId,
        operateType: 30,
      });
      emits('handleClickDown', data);
      uni.hideLoading();
    }
  }
  else {
    if (isLastQuestion) {
      // 认定点最后一题
      const { data } = await BrushApi.findAdjacentTree({
        exercisesPlanId,
        questionLibraryId,
        requestType,
        treeId: libraryItemPointInfo.parentKnowledgeTreeId,
        operateType: 20,
      });
      if (data) {
        console.log(data, '存在下个认定点');
        uni.showModal({
          title: '提示', // 模态框标题
          content: `当前类目试题已${
          requestType === 3 ? '看' : '答'
        }完，是否进入下一类目?`, // 模态框内容
          showCancel: true, // 是否显示取消按钮，默认为true
          cancelText: '取消', // 取消按钮的文字
          confirmText: '确定', // 确定按钮的文字
          success: ({ confirm }) => {
            if (confirm) {
            // 执行确认后的逻辑
              console.log('进入下一类目');
              (async () => {
                await enterNewKonwledgePoint(data.lastItemId, data.treeId);
              })();
            }
          },
        });
      }
      else {
        // 题库最后一题
        console.log('已无下个认定点');
        uni.showToast({
          title: '当前题库已无下一题',
          icon: 'none',
          duration: 2000,
        });
      }
    }
    else {
      // 认定点非最后一题，获取下一题
      uni.showLoading({
        title: '加载中',
      });
      const { data } = await BrushApi.findNextLibraryItem({
        exercisesPlanId,
        questionLibraryId,
        requestType,
        sequenceNum,
        parentKnowledgeTreeId,
        operateType: 20,
      });
      emits('handleClickDown', data);
      uni.hideLoading();
    }
  }
};
const handleCollect = async () => {
  console.log('点击收藏');
  const {
    exercisesPlanId,
    questionLibraryId,
    id: questionExercisesLibraryItemId,
    answerExercisesQuestion,
    sequenceNum,
    parentKnowledgeTreeId,
  } = props.paperInfo;
  const { requestType } = props.queryInfo;
  const res = await BrushApi.saveOrUpdateCollection({
    exercisesPlanId,
    questionLibraryId,
    questionExercisesLibraryItemId,
    answerExercisesQuestion,
  });
  if (res && res.code === 0) {
    if (res.data === 20) {
      uni.showLoading({
        title: '加载中',
      });
      const result = await BrushApi.findNextLibraryItem({
        exercisesPlanId,
        questionLibraryId,
        requestType,
        parentKnowledgeTreeId,
        sequenceNum,
        operateType: 30,
      });
      uni.hideLoading();
      if (result) {
        emits('handleCollect', result.data);
      }
      else {
        emits('handleCollect', {});
      }
      // 取消收藏 在我的收藏页面需要重新查数据
      uni.showToast({
        title: '取消收藏成功',
        icon: 'success',
        duration: 2000,
      });
    }
    else {
      uni.showLoading({
        title: '加载中',
      });
      const { data } = await BrushApi.findNextLibraryItem({
        exercisesPlanId,
        questionLibraryId,
        requestType,
        parentKnowledgeTreeId,
        sequenceNum,
        operateType: 30,
      });
      uni.hideLoading();
      emits('handleCollect', data);
      uni.showToast({
        title: '收藏成功',
        icon: 'success',
        duration: 2000,
      });
    }
  }
};
// 刷题模式end

// 模拟测试模式start
const handleShowAnswerCard = () => {
  console.log('点击答题卡按钮');
  emits('handleShowAnswerCard');
};
const submit = async () => {
  const { token } = props.queryInfo;
  const {
    questionExercisesPaperStatisticsId: id,
    exercisesPlanId,
    paperId,
  } = props.paperInfo;
  const { data } = await ExamApi.submitPaper({
    id,
    exercisesPlanId,
    paperId,
    token,
  });
  uni.showToast({
    title: '交卷成功',
    icon: 'success',
    duration: 2000,
  });
  emits('handleClickSubmit', {
    exercisesPaperScore: data.exercisesPaperScore,
    exercisesPaperComment: data.exercisesPaperComment,
  });
};
const examSubmitCurrentQuestion = async () => {
  const { id: questionExercisesPaperItemId } = props.paperInfo;
  const { requestType } = props.queryInfo;
  if (requestType !== 6 && answerFormatter.value) {
    uni.showLoading({
      title: '加载中',
    });
    await ExamApi.checkStageSubmit({
      questionExercisesPaperItemId,
      answerExamQuestion: answerFormatter.value,
    });
    uni.hideLoading();
  }
};
const handleClickSubmit = async () => {
  console.log('点击交卷按钮');
  const { questionExercisesPaperStatisticsId: id } = props.paperInfo;
  examSubmitCurrentQuestion();
  const res = await ExamApi.checkPaperAnswerAll({
    paperStatisticsId: id,
  });
  uni.showModal({
    title: '提示', // 模态框标题
    content: res.data
      ? '当前测试卷所有试题已答完，是否交卷?'
      : '当前模拟卷仍有试题未答完，是否确认提交试卷?', // 模态框内容
    showCancel: true, // 是否显示取消按钮，默认为true
    cancelText: '取消', // 取消按钮的文字
    confirmText: '确定', // 确定按钮的文字
    success: ({ confirm, cancel }) => {
      if (confirm) {
        // 执行确认后的逻辑
        submit();
      }
      if (cancel) {
        // 执行取消后的逻辑
      }
    },
  });
};
const examClickUp = async () => {
  console.log('模拟考试上一题');
  const {
    questionExercisesPaperStatisticsId,
    exercisesPlanId,
    paperId,
    topicId,
    sequenceNum,
  } = props.paperInfo;
  await examSubmitCurrentQuestion();
  if (sequenceNum === 1) {
    uni.showToast({
      title: '已是当前试卷第一题',
      icon: 'none',
      duration: 2000,
    });
  }
  else {
    const { data } = await ExamApi.findNextItem({
      questionExercisesPaperStatisticsId,
      exercisesPlanId,
      paperId,
      topicId,
      sequenceNum,
      operateType: 10,
    });
    console.log(data, '下一题');
    emits('handleClickUp', data);
  }
};
const examClickDown = async () => {
  console.log('模拟考试下一题');
  const {
    questionExercisesPaperStatisticsId,
    exercisesPlanId,
    paperId,
    topicId,
    sequenceNum,
    paperItemPointInfo,
  } = props.paperInfo;
  const { lastFlag } = paperItemPointInfo;
  const { requestType } = props.queryInfo;

  await examSubmitCurrentQuestion();
  if (lastFlag) {
    if (requestType === 6) {
      return uni.showToast({
        title: '已是当前试卷最后一题',
        icon: 'none',
        duration: 2000,
      });
    }
    const { data } = await ExamApi.checkPaperAnswerAll({
      paperStatisticsId: questionExercisesPaperStatisticsId,
    });
    uni.showModal({
      title: '提示', // 模态框标题
      content: data
        ? '当前测试卷所有试题已答完，是否交卷?'
        : '当前模拟卷仍有试题未答完，是否确认提交试卷?', // 模态框内容
      showCancel: true, // 是否显示取消按钮，默认为true
      cancelText: '取消', // 取消按钮的文字
      confirmText: '确定', // 确定按钮的文字
      success: ({ confirm, cancel }) => {
        if (confirm) {
          // 执行确认后的逻辑
          submit();
        }
        if (cancel) {
          // 执行取消后的逻辑
          uni.showToast({
            title: '已是当前试卷最后一题',
            duration: 2000,
          });
        }
      },
    });
  }
  else {
    const { data } = await ExamApi.findNextItem({
      questionExercisesPaperStatisticsId,
      exercisesPlanId,
      paperId,
      topicId,
      sequenceNum,
      operateType: 20,
    });
    console.log(data, '下一题');
    emits('handleClickDown', data);
  }
};
// 模拟测试模式end
const handleClickUp = async () => {
  console.log('点击上一题');
  if (props.paperType === paperType.brush) {
    await brushClickUp();
  }
  else {
    await examClickUp();
  }
};
const handleClickDown = async () => {
  console.log('点击下一题');
  if (props.paperType === paperType.brush) {
    await brushClickDown();
  }
  else {
    await examClickDown();
  }
};
</script>

<style lang="scss" scoped></style>
