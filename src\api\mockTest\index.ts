/**
 * 模拟测试配置相关接口
 */
import type { mainParams, recordParams } from './types'
import { get } from '@/utils/request'
import type { CommonResult } from '@/api/common/types'

enum URL {
    findPaperMainInfo = 'question/exercises-paper-statistics/weChatMiniProgram/findPaperMainInfo',
    mockRecord = 'question/exercises-paper-statistics/weChatMiniProgram/page',
}
export const findPaperMainInfo = (params: mainParams) => get<CommonResult>({ url: URL.findPaperMainInfo, params })
export const getMockRecord = (params: recordParams) => get<CommonResult>({ url: URL.mockRecord, params })
