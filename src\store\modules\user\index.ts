import { defineStore } from 'pinia';
import type { UserState } from './types';
import { LoginApi } from '@/api';
import { getRefreshToken,clearToken } from '@/utils/auth';
const useUserStore = defineStore('user', {
	state: () : UserState => ({
		user_id: '',
		user_name: '江阳小道',
		avatar: '',
		token: '',
	}),
	getters: {
		userInfo(state : UserState) : UserState {
			return { ...state };
		},
	},
	actions: {
		async refreshToken() {
			const params = {
				refreshToken: getRefreshToken()
			}
			return await LoginApi.refreshToken(params)
		},
        async logout(){
            // 退出登录
            await LoginApi.logout()
            clearToken()
            uni.reLaunch({
                url: '/pages/common/login/index'
            })
        }
	},
});

export default useUserStore;