<template>
  <da-tree
    :data="treeData"
    label-field="name"
    value-field="id"
    leaf-field="lastTreeFlag"
    children-field="childNodeList"
    :show-radio-icon="false"
    :show-total="false"
    :default-expanded-keys="defaultExpandedKeys"
    :default-checked-keys="defaultCheckedKeys"
    @change="handleTreeChange"
  />
</template>

<script setup lang="ts">
const props = defineProps({
  knowledgeTreeInfo: {
    type: Array,
    requred: true,
  },
  defaultExpandedKeys: {
    type: Array,
  },
  defaultCheckedKeys: {
    type: Number,
  },
  defaultExpandAll: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['change']);
const treeData: Ref<any> = ref([]);

const handleTreeChange = (values: number, currentItem: any) => {
  const item = currentItem.originItem;
  emit('change', {
    currentTreeId: values,
    lastTreeFlag: item.lastTreeFlag,
    parentKnowledgeTreeId: item.lastTreeFlag ? values : undefined,
    sequenceNum: item.knowledgeTreePointInfo.sequenceNum,
    operateType: item.knowledgeTreePointInfo.sequenceNum < item.knowledgeTreePointInfo.questionTotal ? 20 : 30,
  });
};

treeData.value = props.knowledgeTreeInfo;
</script>
