<!-- eslint-disable ts/no-use-before-define -->
<template>
  <view v-if="Object.keys(paperInfo).length > 0" class="exam-box bg-white">
    <!-- <up-radio-group v-model="value">
      <up-radio activeColor="red" labelColor="red" label="思悠悠，恨悠悠，恨到归时方始休"></up-radio>
    </up-radio-group> -->

    <view>
      <view class="header flex items-center bg-white pa-30rpx">
        <up-icon
          name="list-dot"
          color="#4076F6"
          size="24"
          @click="showTree"
        />
        <text class="ml-8rpx text-28rpx text-#333">
          知识点:{{ paperInfo.parentKnowledgeTreeName }}
        </text>
      </view>
      <view class="px-30rpx py-36rpx">
        <view class="flex">
          <text class="quesiton-type">
            {{
              questionType[paperInfo.questionTypeCode]
            }}
          </text>
          <text class="text-32rpx text-#333">
            {{ paperInfo.sequenceNum }}、{{ paperInfo.stem }}
          </text>
        </view>
        <view class="pl-30rpx">
          <!-- :disabled="answerStatus !== 10 || requestType == 3"  -->
          <up-radio-group
            v-if="
              paperInfo.questionTypeCode === 'B'
                || paperInfo.questionTypeCode === 'C'
            "
            v-model="paperInfo.answerExercisesQuestion"
            class="quesition-group"
            label-size="32rpx"
            label-color="#333"
            placement="column"
            :class="(answerStatus !== 10 || requestType == 3) && 'not-working'"
          >
            <view
              v-for="(item, index) in paperInfo.checkOption"
              :key="index.toString().slice(-1)"
              :class="[
                index.toString().slice(-1) == paperInfo.answerExercisesQuestion
                  ? paperInfo.answerExercisesQuestion == paperInfo.paperAnswer
                    ? 'success-answer'
                    : 'error-answer'
                  : '',
              ]"
            >
              <up-radio
                v-if="item"
                :custom-style="{ marginTop: '48rpx' }"
                :label="`${index.toString().slice(-1)}、${item}`"
                :name="index.toString().slice(-1)"
              />
            </view>
          </up-radio-group>
          <!--  :disabled="answerStatus !== 10 || requestType == 3" -->
          <up-checkbox-group
            v-if="paperInfo.questionTypeCode === 'D'"
            v-model="paperInfo.answerExercisesQuestion"
            class="quesition-group"
            placement="column"
            label-size="32rpx"
            label-color="#333"
            :class="(answerStatus !== 10 || requestType == 3) && 'not-working'"
          >
            <view
              v-for="(item, index) in paperInfo.checkOption"
              :key="index.toString().slice(-1)"
              :class="[
                paperInfo.answerExercisesQuestion?.includes(
                  index.toString().slice(-1),
                ) && 'error-answer',
                paperInfo.answerExercisesQuestion?.includes(
                  paperInfo.paperAnswer
                    ?.split('')
                    .filter(
                      (item: string) => item == index.toString().slice(-1),
                    )[0],
                ) && 'success-answer',
              ]"
            >
              <up-checkbox
                v-if="item"
                :custom-style="{ marginTop: '48rpx' }"
                :label="`${index.toString().slice(-1)}、${item}`"
                :name="index.toString().slice(-1)"
              />
            </view>
          </up-checkbox-group>
        </view>
        <view v-if="analysisShow" class="mt-85rpx text-28rpx text-#333">
          <view
            class="mb-48rpx flex items-center justify-around text-align-center"
          >
            <up-line color="#EEEEEE" length="30%" /><text>试题解析</text><up-line color="#EEEEEE" length="30%" />
          </view>
          <view class="left-border">
            正确答案<text class="ml-20rpx text-#4076F6">
              {{
                paperInfo.questionTypeCode === "D"
                  ? paperInfo.paperAnswer.split("").join("、")
                  : paperInfo.paperAnswer
              }}
            </text>
          </view>
          <view class="left-border my-48rpx">
            你的答案<text
              class="ml-20rpx" :class="[
                answerStatus == 30 ? 'text-#FE474A' : 'text-#4076F6',
              ]"
            >
              {{
                paperInfo.questionTypeCode === "D"
                  ? paperInfo.answerExercisesQuestion.length
                    ? paperInfo.answerExercisesQuestion.join("、")
                    : ""
                  : paperInfo.answerExercisesQuestion
              }}
            </text>
          </view>

          <view class="left-border text-#999">
            试题解析
            <view class="mt-16rpx text-#333">
              {{
                paperInfo.paperQuestionAnalysis
              }}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view
      v-if="paperInfo && paperInfo.libraryItemPointInfo"
      class="flex justify-between border-t-1px border-t-#E5E5E5 border-t-solid px-30rpx py-36rpx pt-20rpx"
    >
      <view class="flex items-center">
        <view class="text-36rpx">
          <text class="text-#333">
            {{ paperInfo.sequenceNum }}
          </text><text class="text-#999">
            /{{ paperInfo.libraryItemPointInfo.questionTotal }}
          </text>
        </view>
        <view class="mx-40rpx">
          <up-icon
            :name="
              paperInfo.libraryItemPointInfo.collectFlag ? 'star-fill' : 'star'
            "
            :label="
              paperInfo.libraryItemPointInfo.collectFlag ? '取消收藏' : '收藏'
            "
            size="44rpx"
            label-pos="bottom"
            label-size="24rpx"
            label-color="#999"
            :color="paperInfo.libraryItemPointInfo.collectFlag && 'orange'"
            @click="handleCollect(paperInfo)"
          />
        </view>
      </view>
      <view class="flex items-center">
        <up-button
          type="primary"
          text="上一题"
          shape="circle"
          plain
          :custom-style="{
            marginRight: '20rpx',
            width: '146rpx',
            height: '54rpx',
          }"
          :disabled="paperInfo.libraryItemPointInfo.sequenceNum === 1"
          @click="
            getNextQuestion(
              paperInfo.libraryItemPointInfo.sequenceNum,
              10,
              paperInfo.id,
              paperInfo.parentKnowledgeTreeId,
            )
          "
        />
        <up-button
          type="primary"
          text="下一题"
          shape="circle"
          :custom-style="{ width: '146rpx', height: '54rpx' }"
          @click="
            getNextQuestion(
              paperInfo.libraryItemPointInfo.sequenceNum,
              20,
              paperInfo.id,
              paperInfo.parentKnowledgeTreeId,
            )
          "
        />
      </view>
    </view>
  </view>
  <view v-if="Object.keys(paperInfo).length == 0 && requestType == 1">
    <view class="no-empty bg-white">
      <image src="/static/images/no-collect.png" />
    </view>
  </view>
  <view v-if="Object.keys(paperInfo).length == 0 && requestType == 2">
    <view class="no-empty bg-white">
      <image src="/static/images/no-wrong.png" />
      <view>暂无错题</view>
    </view>
  </view>
  <up-popup
    :show="popupShow"
    mode="left"
    :custom-style="{ width: '300px' }"
    @close="close"
  >
    <da-tree
      ref="DaTreeRef"
      :data="treeData"
      label-field="name"
      value-field="id"
      children-field="childNodeList"
      :show-radio-icon="false"
      :show-total="false"
      :default-expanded-keys="defaultExpandedKeys"
      :default-checked-keys="defaultCheckedKeys"
      @change="handleTreeChange"
    />
  </up-popup>
</template>

<script setup lang="ts">
import { BrushApi } from '@/api';

const exercisesPlanId = ref(); // 计划id
const questionLibraryId = ref(); // 题库id
const requestType = ref(); // 请求类型
const careerCode = ref(); // 职业工种等级编码
const analysisShow = ref(false);
const popupShow = ref(false);
const answerStatus = ref(10); // 10未答 20正确 30错误
const questionTotal = ref();
const noDataSrc = {
  1: '/static/images/no-collect.png',
  2: '/static/images/no-wrong.png',
};

const titleMap = {
  1: '我的收藏',
  2: '我的错题',
  3: '背题模式',
};
const questionType: any = {
  B: '单选题',
  D: '多选题',
  C: '判断题',
  E: '简答题',
  F: '论述题',
};
const paperInfo = ref<any>({});
const treeData = ref([]);
const DaTreeRef = ref();
const defaultExpandedKeys = ref([]);
const defaultCheckedKeys = ref();
const showTree = () => {
  getTreeAll();
  // DaTreeRef.value.setExpandedKeys([paperInfo.value.parentKnowledgeTreeId], true);
  // DaTreeRef.value.setCheckedKeys([paperInfo.value.parentKnowledgeTreeId], true);
};
const handleTreeChange = (values: any, currentItem: { originItem: any }) => {
  // 支持修改节点数据
  const item = currentItem.originItem;
  getQuestion(item.knowledgeTreePointInfo.sequenceNum, 20, values);
  popupShow.value = false;
};

// 通过题目id查询题目
const getQuestionById = async (libraryItemId: any) => {
  const params = {
    libraryItemId,
    requestType: requestType.value,
  };
  const res = await BrushApi.findLibraryItemByRequestType(params);
  paperInfo.value = res.data;
  answerStatus.value = res.data.answerStatus;

  if (res.data.questionTypeCode === 'D') {
    paperInfo.value.answerExercisesQuestion = [];
  }
  if (requestType.value == 4) {
    uni.setNavigationBarTitle({
      title: paperInfo.value.parentKnowledgeTreeName,
    });
  }
};
// 查询上一题下一题
const getNextQuestion = async (
  sequenceNum: number,
  operateType: number,
  id: null | undefined,
  parentKnowledgeTreeId: undefined,
) => {
  if (id) {
    if (operateType == 20 && !analysisShow.value) {
      // 下一题之前校验答案 展示解析
      const params = {
        questionExercisesLibraryItemId: id,
        requestType: requestType.value,
        answerExamQuestion:
          paperInfo.value.questionTypeCode === 'D'
            ? paperInfo.value.answerExercisesQuestion.join(',')
            : paperInfo.value.answerExercisesQuestion,
      };
      const res = await BrushApi.checkLibraryStageSubmit(params);
      if (res.code === 0) {
        answerStatus.value = res.data.answerStatus;
        analysisShow.value = true;
      }
    }
    else {
      getQuestion(sequenceNum, operateType, parentKnowledgeTreeId);
    }
  }
  else {
    // 第一次进入
    getQuestion(sequenceNum, operateType);
  }
};
const getQuestion = async (
  sequenceNum: any,
  operateType: number,
  parentKnowledgeTreeId: undefined,
) => {
  uni.showLoading({
    title: '加载中',
  });
  const params = {
    exercisesPlanId: exercisesPlanId.value,
    questionLibraryId: questionLibraryId.value,
    sequenceNum,
    operateType,
    requestType: requestType.value,
    parentKnowledgeTreeId: parentKnowledgeTreeId || null,
  };
  const res = await BrushApi.findNextLibraryItem(params);
  if (res && res.code == 0) {
    console.log(12456789);
    uni.hideLoading();
    paperInfo.value = res.data;
    answerStatus.value = res.data.answerStatus;
    if (operateType == 20) {
      analysisShow.value = false;
    }
    else {
      analysisShow.value = true;
    }
    paperInfo.value.result = {};
    if (res.data.questionTypeCode === 'D') {
      paperInfo.value.answerExercisesQuestion
        = paperInfo.value.answerExercisesQuestion?.split(',');
    }
    if (requestType.value == 4) {
      uni.setNavigationBarTitle({
        title: paperInfo.value.parentKnowledgeTreeName,
      });
    }
  }
  else {
    if (requestType.value == 1) {
      // 取消收藏且一道收藏题都没有的时候返回-20 展示无数据列表  请求封装res为undefined所用题目总数判断
      if (questionTotal.value == 1) {
        // 为1说明取消收藏最后一题成功 列表为空
        paperInfo.value = {};
      }
    }
  }
};
// 获取知识树
const getTreeAll = async () => {
  uni.showLoading({
    title: '加载中',
  });
  const params = {
    exercisesPlanId: exercisesPlanId.value,
    questionLibraryId: questionLibraryId.value,
    requestType: requestType.value,
    careerCode: careerCode.value,
  };
  const res = await BrushApi.queryAllTree(params);
  uni.hideLoading();
  treeData.value = res.data;
  popupShow.value = true;
  defaultCheckedKeys.value = paperInfo.value.parentKnowledgeTreeId;
  defaultExpandedKeys.value = [paperInfo.value.parentKnowledgeTreeId];
};
const close = () => {
  popupShow.value = false;
};
// 收藏 取消收藏
const handleCollect = async (item: {
  libraryItemPointInfo: {
    requestType: number;
    questionTotal: any;
    sequenceNum: number;
  };
  parentKnowledgeTreeId: any;
  id: any;
}) => {
  console.log(item, 'item');
  const params = {
    exercisesPlanId: exercisesPlanId.value,
    questionLibraryId: questionLibraryId.value,
    questionExercisesLibraryItemId: paperInfo.value.id,
    answerExercisesQuestion:
      paperInfo.value === 'D'
        ? paperInfo.value.answerExercisesQuestion.join('-')
        : paperInfo.value.answerExercisesQuestion,
  };
  const res = await BrushApi.saveOrUpdateCollection(params);
  if (res && res.code == 0) {
    if (res.data == 20) {
      // 取消收藏 在我的收藏页面需要重新查数据
      uni.showToast({
        title: '取消收藏成功',
        icon: 'success',
      });

      // 收藏模式下 取消收藏
      if (item.libraryItemPointInfo.requestType == 1) {
        // 收藏模式下通过题目总数判断取消成功后有没有试题
        questionTotal.value = item.libraryItemPointInfo.questionTotal;
        if (item.libraryItemPointInfo.sequenceNum == 1) {
          getNextQuestion(0, 20, null, item.parentKnowledgeTreeId);
        }
        else {
          getNextQuestion(
            item.libraryItemPointInfo.sequenceNum,
            10,
            null,
            item.parentKnowledgeTreeId,
          );
        }
      }
      else {
        // 其他模式
        getQuestionById(item.id); // 取消收藏成功 刷新页面获取最新数据
      }
    }
    else {
      uni.showToast({
        title: '收藏成功',
        icon: 'success',
      });
      // 收藏成功 刷新页面获取最新数据
      getQuestionById(item.id);
    }
  }
};
onMounted(() => {
  const { proxy } = getCurrentInstance();
  const options = proxy.options || proxy.$scope.options;
  const queryInfo = JSON.parse(options.queryInfo);
  console.log(queryInfo.libraryItemId, 99999);
  careerCode.value = queryInfo.careerCode;
  if (queryInfo.requestType != 4) {
    uni.setNavigationBarTitle({
      title: titleMap[queryInfo.requestType],
    });
  }
  exercisesPlanId.value = queryInfo.exercisesPlanId;
  questionLibraryId.value = queryInfo.questionLibraryId;
  requestType.value = queryInfo.requestType;

  if (queryInfo.libraryItemId) {
    // 已经存在id  不是第一次 通过id查询题目明细
    getQuestionById(queryInfo.libraryItemId);
  }
  else {
    // 从知识树进入 根据树id查询题目
    if (queryInfo.parentKnowledgeTreeId) {
      const sequenceNum = queryInfo.sequenceNum;
      const operateType = 20;
      const parentKnowledgeTreeId = queryInfo.parentKnowledgeTreeId;
      getQuestion(sequenceNum, operateType, parentKnowledgeTreeId);
    }
    else {
      // 第一次进入题库 查询默认第一题
      const sequenceNum = 0;
      const operateType = 20;
      getNextQuestion(sequenceNum, operateType);
    }
  }
});
</script>

<style scoped lang="scss">
.header {
  box-shadow: 0rpx 0rpx 14rpx 0rpx rgb(0 0 0 / 14%);
}

.exam-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100vh - 44px);
  box-sizing: border-box;

  .quesiton-type {
    display: inline-block;
    padding-left: 16rpx;
    width: 92rpx;
    height: 38rpx;
    font-size: 20rpx;
    background: url("/static/images/mocktest/type-bg.png") no-repeat;
    background-size: 100% 100%;
    line-height: 38rpx;
  }

  .quesition-group {
    .u-radio {
      display: flex;
      align-items: flex-start;

      :deep(uni-text.u-radio__text) {
        flex: 1;
      }
    }
  }

  .left-border {
    position: relative;

    &::before {
      position: absolute;
      top: 6rpx;
      left: -30rpx;
      width: 8rpx;
      height: 28rpx;
      background: linear-gradient(180deg, #74aefb 0%, #4076f6 100%);
      border-radius: 0rpx 6rpx 6rpx 0rpx;
      content: "";
    }
  }
}

.no-empty {
  /* 垂直居中 */
  position: absolute;
  inset: 0;
  display: flex;
  justify-content: center;

  /* 水平居中 */
  align-items: center;
  flex-direction: column;
}

.not-working {
  user-select: none;
  pointer-events: none;

  .error-answer {
    :deep(.u-radio__icon-wrap),
    :deep(.u-checkbox__icon-wrap) {
      background-color: #fe474a !important;
      border-color: #fe474a !important;

      :deep(.u-radio__text),
      :deep(uni-text) {
        color: #fe474a !important;
      }

      :deep(uni-text.u-icon__icon) {
        color: white !important;
      }
    }
  }

  .success-answer {
    :deep(.u-radio__icon-wrap),
    :deep(.u-checkbox__icon-wrap) {
      background-color: #4076f6 !important;
      border-color: #4076f6 !important;

      :deep(.u-radio__text),
      :deep(uni-text) {
        color: #4076f6 !important;
      }

      :deep(uni-text.u-icon__icon) {
        color: white !important;
      }
    }
  }
}
</style>
