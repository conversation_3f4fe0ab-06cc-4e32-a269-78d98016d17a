export interface treeParams {
    exercisesPlanId: number
    questionLibraryId: number
    requestType: number
    careerCode: string
}
export interface nextParams {
    exercisesPlanId: number
    questionLibraryId: number
    parentKnowledgeTreeId?: number
    currentTreeId?: number
    topicId?: number
    sequenceNum: number
    operateType: number
    requestType: number
}
export interface deatailParams {
    libraryItemId: number
    requestType: number
}
export interface checkParams {
    questionExercisesLibraryItemId: number
    requestType: number
    answerExamQuestion?: string
}

export interface collectParams {
    exercisesPlanId: number
    questionLibraryId: number
    questionExercisesLibraryItemId: number
    answerExercisesQuestion: string
}
export interface adjacentTreeParams {
  exercisesPlanId: number
  questionLibraryId: number
  requestType: number
  operateType: number
  treeId: number
}
