<script setup lang="ts">
	import { mpUpdate } from '@/utils/index';
	import { getAccessToken, getToken } from '@/utils/auth';


	onLaunch(() => {
		console.log('App Launch', getToken());
		if (!getAccessToken()) {
			uni.reLaunch({ url: '/pages/common/login/index' });
		}
		// #ifdef MP-WEIXIN
		mpUpdate();
		// #endif
	});
	onShow(() => {
		console.log('App Show');
	});
	onHide(() => {
		console.log('App Hide');
	});
</script>

<style lang="scss">
	/* 每个页面公共css */
	@import 'uview-plus/index.scss';
	@import '@/static/styles/common.scss';
</style>