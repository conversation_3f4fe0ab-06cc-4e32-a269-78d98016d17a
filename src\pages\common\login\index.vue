<template>
  <view class="h-full overflow-y-hidden">
    <view class="header">
      <view class="bg-box">
        <image src="/static/images/logo-new.png" class="login-logo" />
        <image src="/static/images/login-bg.png" class="background-image" />
      </view>
    </view>
    <view class="login-form-wrap h-100% bg-white">
      <view class="title">
        账号密码登录
      </view>
      <up-input
        v-model="account"
        type="text"
        placeholder="请输入登录账号"
        border="bottom"
      />
      <view class="my-40rpx flex">
        <up-input
          v-model="password"
          class="flex-1"
          type="password"
          placeholder="请输入密码"
          border="bottom"
        />
      </view>
      <view>
        <u-checkbox
          v-model:checked="aloneChecked"
          :label-size="12"
          :custom-style="{ margin: '30rpx 0 40rpx 0' }"
          name="agree"
          used-alone
          :label-disabled="true"
        >
          <template #label>
            <view class="checkbox-label flex">
              我已阅读并同意职证宝<view
                class="link"
                @click="jump('/pages/tab/protocol/userAgreement')"
              >
                《用户协议》
              </view>和<view
                class="link"
                @click="jump('/pages/tab/protocol/privacyPolicy')"
              >
                《隐私政策》
              </view>
            </view>
          </template>
        </u-checkbox>
      </view>

      <up-button
        text="登录"
        type="primary"
        shape="circle"
        @click="handleLogin"
      />
      <view class="hint">
        仅供机构内部学员使用，需要提供账号密码进行登录
      </view>
      <!-- <view class="institution"> 创联教育 </view> -->
    </view>
    <up-action-sheet
      :actions="tenantList"
      title="请选择机构"
      :show="tenantSelectShow"
      :safe-area-inset-bottom="true"
      @select="tenantSelect"
      @close="tenantSelectShow = false"
    />
  </view>
</template>

<script setup lang="ts">
import { setToken } from '@/utils/auth';
import { LoginApi } from '@/api';
// import { useUserStore } from '@/store';

// const userStore = useUserStore();

// #ifdef MP-WEIXIN
if (wx.hideHomeButton) {
  wx.hideHomeButton();
}
// #endif

const account = ref<string>('');
const password = ref<string>('');
const aloneChecked = ref(false);
const tenantList = ref<any>([]);
const tenantSelectShow = ref(false);
const handleLogin = async () => {
  if (!account.value) {
    uni.$u.toast('请输入登录账号');
    return;
  }
  if (!password.value) {
    uni.$u.toast('请输入密码');
    return;
  }
  if (!aloneChecked.value) {
    uni.$u.toast('请先勾选协议');
    return;
  }

  // 机构
  if (import.meta.env.VITE_APP_TENANT_ENABLE == 'true') {
    getTenantId(account.value);
  }
  else {
    login();
  }
};
const getTenantId = async (value: string) => {
  const params = {
    account: value,
  };
  await LoginApi.getTenantId(params).then((res: any) => {
    tenantList.value = res.data.filter(
      (item: any) => (item.name = item.tenantName),
    );
    const tenantLength: number = tenantList.value.length;
    if (!tenantLength) {
      return uni.showToast({
        title: '机构不存在，请检查账号是否正确',
        icon: 'none',
        duration: 2000,
      });
    }
    // 只有一个机构, 直接登录
    if (tenantLength === 1) {
      uni.setStorageSync('tenantId', tenantList.value[0].tenantId);
      login();
    }
    else {
      // 多个机构
      tenantSelectShow.value = true;
    }
  });
};
const tenantSelect = (e: any) => {
  uni.setStorageSync('tenantId', e.tenantId);
  login();
};
const login = async () => {
  const params = {
    account: account.value,
    password: password.value,
  };
  await LoginApi.login(params).then((res) => {
    console.log(res, 'loginres');
    if (res.code == 0) {
      setToken(res.data);
      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          uni.reLaunch({ url: '/pages/tab/home/<USER>' });
        },
      });
    }
  });
};

const jump = (url: string) => {
  uni.navigateTo({
    url,
  });
};
</script>

<style lang="scss" scoped>
.login-form-wrap {
  @apply mt-0rpx mx-auto mb-0 px-40rpx;

  border-top-right-radius: 36rpx;
  border-top-left-radius: 36rpx;

  input {
    @apply pb-6rpx mb-10rpx text-left;
  }

  .tips {
    @apply mt-8rpx mb-60rpx;

    color: $u-info;
  }

  .title {
    padding: 50rpx 10rpx;
    font-size: 40rpx;
    font-weight: 500;
  }

  .checkbox-label {
    font-size: 28rpx;
    color: #999;
  }

  .login-btn {
    @apply flex items-center justify-center py-12rpx px-0 text-30rpx bg-#fdf3d0 border-none;

    color: $u-tips-color;

    &::after {
      @apply border-none;
    }
  }

  .alternative {
    @apply flex justify-between mt-30rpx;

    color: $u-tips-color;
  }
}

.login-type-wrap {
  @apply flex justify-between pt-350rpx px-150rpx pb-150rpx;

  .item {
    @apply flex items-center flex-col text-28rpx;

    color: $u-content-color;
  }
}

.hint {
  @apply text-center mt-20rpx text-28rpx;

  color: #666;

  .link {
    color: $u-warning;
  }
}

.institution {
  @apply text-center mt-64rpx text-28rpx;

  color: #666;
}

.header {
  @apply flex items-center text-60rpx text-center font-500;

  justify-content: space-evenly;

  .bg-box {
    position: relative;
    width: 100%;
    height: 470rpx;

    .login-logo {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 99;
      width: 274rpx;
      height: 274rpx;
      transform: translate(-50%, -50%);
    }

    .background-image {
      position: absolute;
      inset: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.link {
  color: $u-primary;
  flex-shrink: 0;
  font-size: 26rpx;
}
</style>
