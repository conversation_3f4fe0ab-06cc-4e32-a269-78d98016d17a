/**
 * 登录相关接口
 */
import type { TenantParams, LoginParams, refreshParams } from './types';
import { get, post } from '@/utils/request';
import type { CommonResult } from '@/api/common/types';

enum URL {
	login = 'question/student/auth/login',
	logout = 'question/student/auth/logout',
	tenant = 'question/student/auth/query-tenant-by-account',
	refresh = 'question/student/auth/refresh-token'
}

export const login = (data: LoginParams) => post<CommonResult>({ url: URL.login, data });
export const logout = () => post<CommonResult>({ url: URL.logout });
export const getTenantId = (params: TenantParams) => get<CommonResult>({ url: URL.tenant, params });
export const refreshToken = (data: refreshParams) => post<CommonResult>({ url: URL.refresh, data });