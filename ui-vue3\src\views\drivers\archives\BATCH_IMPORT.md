# 司机档案批量导入功能

## 功能概述

实现了司机档案的批量导入功能，包含两个步骤：
1. **下载导入模板** - 获取标准的Excel模板文件
2. **导入数据** - 上传填写完成的Excel文件进行批量导入

## 界面展示

批量导入功能通过弹窗形式展示，包含：
- 第一步：下载导入模板按钮
- 第二步：导入数据按钮
- 文件上传状态提示

## 技术实现

### 组件结构

1. **`components/batchAdd.vue`** - 批量导入主组件
2. **`list.vue`** - 父组件，包含批量导入弹窗
3. **`list.api.ts`** - API接口定义

### 主要功能

#### 1. 模板下载
- 调用后端API获取Excel模板文件
- 支持Blob格式文件下载
- 自动触发浏览器下载

```typescript
const downloadTemplate = async () => {
  const response = await downloadTemplateApi();
  const blob = new Blob([response], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  });
  // 创建下载链接...
};
```

#### 2. 文件上传验证
- 支持 `.xlsx` 和 `.xls` 格式
- 文件大小限制 10MB
- 实时文件类型验证

```typescript
const beforeUpload = async (file: File) => {
  // 文件类型验证
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel' ||
                  file.name.toLowerCase().endsWith('.xlsx') ||
                  file.name.toLowerCase().endsWith('.xls');
  
  // 文件大小验证
  const isLt10M = file.size / 1024 / 1024 < 10;
  
  // 调用导入API...
};
```

#### 3. 批量导入
- 自动调用后端批量导入接口
- 显示导入进度提示
- 导入成功后刷新列表数据

### API接口

#### 下载模板接口
```typescript
/**
 * 下载导入模板
 */
export const downloadTemplate = () => {
  return defHttp.get({ 
    url: Api.downloadTemplate,
    responseType: 'blob'
  });
};
```

#### 批量导入接口
```typescript
/**
 * 批量导入司机档案
 */
export const batchImport = (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  
  return defHttp.post({
    url: Api.batchImport,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};
```

## 使用流程

### 1. 打开批量导入弹窗
点击列表页面的"批量新增"按钮，打开批量导入弹窗。

### 2. 下载模板
1. 点击"下载模板"按钮
2. 系统自动下载Excel模板文件
3. 根据模板格式填写司机档案信息

### 3. 导入数据
1. 点击"导入数据"按钮
2. 选择填写完成的Excel文件
3. 系统自动验证文件格式和大小
4. 调用后端接口进行批量导入
5. 显示导入结果并刷新列表

## 错误处理

### 文件验证错误
- 文件格式不正确：提示"只能上传Excel文件！"
- 文件过大：提示"文件大小不能超过10MB！"

### 导入错误
- 网络错误：提示"导入失败！"
- 服务器错误：显示具体错误信息
- 数据格式错误：根据后端返回的错误信息提示

## 状态管理

### 组件状态
- `uploadedFile` - 当前上传的文件对象
- 加载状态 - 通过 `message.loading()` 显示

### 事件通信
- `@success` - 导入成功事件，传递文件对象
- `@cancel` - 取消操作事件

## 样式设计

### 布局结构
```less
.batch-add-container {
  .step-item {
    margin-bottom: 32px;
    
    .step-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }
    
    .step-description {
      color: #666;
      font-size: 14px;
      margin-bottom: 16px;
    }
    
    .step-action {
      display: flex;
      justify-content: flex-end;
    }
  }
}
```

### 响应式设计
- 弹窗宽度：600px
- 弹窗高度：400px
- 支持移动端适配

## 扩展功能

### 可扩展的功能点
1. **导入进度条** - 显示详细的导入进度
2. **错误详情** - 显示具体的数据错误信息
3. **导入预览** - 导入前预览数据内容
4. **批量验证** - 导入前进行数据格式验证
5. **导入历史** - 记录导入操作历史

### 配置选项
- 支持的文件格式可配置
- 文件大小限制可配置
- 导入字段映射可配置

## 注意事项

1. **模板格式** - 确保后端提供的模板格式与前端验证一致
2. **文件编码** - 注意Excel文件的编码格式
3. **数据验证** - 前端验证与后端验证保持一致
4. **错误处理** - 提供友好的错误提示信息
5. **性能优化** - 大文件导入时考虑分批处理
