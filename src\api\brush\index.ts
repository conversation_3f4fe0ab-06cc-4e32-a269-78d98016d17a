/**
 * 刷题配置相关接口
 */
import type {
  adjacentTreeParams,
  checkParams,
  collectParams,
  deatailParams,
  nextParams,
  treeParams,
} from './types';
import { get, post } from '@/utils/request';
import type { CommonResult } from '@/api/common/types';

enum URL {
  queryAllTree = 'question/exercises-knowledge-tree/weChatMiniProgram/queryAllTree',
  findNextLibraryItem = 'question/exercises-library-item/weChatMiniProgram/findNextLibraryItem', // 获得题库练习明细-下一题/上一题
  findLibraryItemByRequestType = 'question/exercises-library-item/weChatMiniProgram/findLibraryItemByRequestType', // 通过题库题目ID获得详细信息
  checkLibraryStageSubmit = 'question/exercises-library-item/weChatMiniProgram/checkLibraryStageSubmit', // 检查题库阶段提交
  saveOrUpdateCollection = 'question/exercises-collection/weChatMiniProgram/saveOrUpdateCollection', // 收藏 取消收藏
  findAdjacentTree = '/question/exercises-knowledge-tree/weChatMiniProgram/findAdjacentTree', // 获得题库当前层级相邻的树,同层级树ID(上一个树/下一个树)
}
export const queryAllTree = (params: treeParams) =>
  get<CommonResult>({ url: URL.queryAllTree, params });

export const findNextLibraryItem = (params: nextParams) =>
  get<CommonResult>({ url: URL.findNextLibraryItem, params });

export const findLibraryItemByRequestType = (params: deatailParams) =>
  get<CommonResult>({ url: URL.findLibraryItemByRequestType, params });

export const checkLibraryStageSubmit = (data: checkParams) =>
  post<CommonResult>({ url: URL.checkLibraryStageSubmit, data });

export const saveOrUpdateCollection = (data: collectParams) =>
  post<CommonResult>({ url: URL.saveOrUpdateCollection, data });

export const findAdjacentTree = (data: adjacentTreeParams) =>
  get<CommonResult>({ url: URL.findAdjacentTree, data });
