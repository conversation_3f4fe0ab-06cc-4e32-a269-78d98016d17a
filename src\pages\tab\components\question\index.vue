<template>
  <view>
    <single-choice-question
      v-if="questionInfoFormatter.type === questionType.singleChoice"
      v-model="answer"
      :question-info="questionInfoFormatter"
    />
    <multiple-choice-question
      v-if="questionInfoFormatter.type === questionType.multipleChoice"
      v-model="answer"
      :question-info="questionInfoFormatter"
    />
    <judgment-question
      v-if="questionInfoFormatter.type === questionType.judgment"
      v-model="answer"
      :question-info="questionInfoFormatter"
    />
  </view>
</template>

<script setup lang="ts">
// 引入 start
import { watchEffect } from 'vue';
import singleChoiceQuestion from './singleChoiceQuestion.vue';
import multipleChoiceQuestion from './multipleChoiceQuestion.vue';
import judgmentQuestion from './judgmentQuestion.vue';
// 引入 end
// 参数接收 start
const props = defineProps({
  paperInfo: {
    type: Object,
    requred: true,
  },
  requestType: {
    type: Number,
    required: true,
  },
});
// 参数接收end
// 类型定义start
/**
 * 题目状态
 */
enum Status {
  unselected, // 未选择
  selected, // 已选择
  feedback, // 答案反馈
}
/**
 * 题目类型
 */
enum questionType {
  singleChoice,
  multipleChoice,
  judgment,
}
/**
 * 选项
 */
interface OptionsItem {
  label: string;
  value: string;
}
/**
 * 题目信息
 */
interface QuestionInfo {
  type: questionType;
  status: Status;
  question: string;
  sequenceNum: number;
  answer: string | string[] | undefined;
  standardAnswer?: string | string[];
  options: Array<OptionsItem>;
}
// 类型定义end
const formatterQuestionType = (questionTypeCode: string) => {
  switch (questionTypeCode) {
    case 'B':
      return questionType.singleChoice;
    case 'C':
      return questionType.judgment;
    case 'D':
      return questionType.multipleChoice;
    default:
      return questionType.singleChoice;
  }
};
const formatterOptions = (checkOption: { [key: string]: string }) => {
  return Object.keys(checkOption || [])
    .map((key) => {
      return {
        label: key.slice(-1),
        value: checkOption[key],
      };
    })
    .filter(item => item.value);
};
const formatterAnswer = (answer: string | undefined) => {
  const type = formatterQuestionType(props.paperInfo!.questionTypeCode);
  switch (type) {
    case questionType.judgment:
    case questionType.singleChoice:
      return answer;
    case questionType.multipleChoice:
      if (answer?.includes(',')) {
        return answer?.split(',');
      }
      else {
        return answer?.split('');
      }
    default:
      return '';
  }
};
const formatterStatus = (answerStatus: number, requestType: number) => {
  if (requestType === 5) {
    return Status.unselected;
  }
  if (requestType === 6 || requestType === 3) {
    return Status.feedback;
  }
  if (answerStatus !== 10) {
    return Status.feedback;
  }
  else {
    return Status.unselected;
  }
};
const questionInfoFormatter = ref<QuestionInfo>({
  type: questionType.singleChoice,
  status: Status.unselected,
  question: '',
  sequenceNum: 0,
  answer: '',
  options: [],
});
const answer = defineModel();

watchEffect(() => {
  const {
    questionTypeCode,
    stem,
    sequenceNum,
    paperAnswer,
    answerExercisesQuestion,
    checkOption,
    answerStatus,
  } = props.paperInfo!;
  questionInfoFormatter.value = {
    type: formatterQuestionType(questionTypeCode),
    question: stem,
    sequenceNum,
    answer:
      props.requestType === 3
        ? formatterAnswer(paperAnswer)
        : formatterAnswer(answerExercisesQuestion),
    standardAnswer: formatterAnswer(paperAnswer),
    // paperInfo.answerStatus：10-未答题 20-正确 30-错误
    // props.requestType： 3-背题模式
    status: formatterStatus(answerStatus, props.requestType),
    options: formatterOptions(checkOption),
  };
  answer.value = props.requestType === 3
    ? formatterAnswer(paperAnswer)
    : formatterAnswer(answerExercisesQuestion);
  console.log(questionInfoFormatter.value, 'questionInfoFormatter');
});
</script>

<style lang="scss" scoped></style>
