<template>
  <j-modal :maskClosable="false" :title="title" :width="width" :visible="visible" @ok="handleOk" :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }" @cancel="handleCancel" cancelText="关闭">
    <HrDriverReportForm ref="registerForm" @ok="submitCallback" :formDisabled="disableSubmit" :formBpm="false"></HrDriverReportForm>
  </j-modal>
</template>

<script lang="ts" setup>
  import { ref, nextTick, defineExpose } from 'vue';
  import HrDriverReportForm from './HrDriverReportForm.vue'
  import JModal from '/@/components/Modal/src/JModal/JModal.vue';
  
  const title = ref<string>('');
  const width = ref<number>(1400);
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const registerForm = ref();
  const emit = defineEmits(['register', 'success']);

  /**
   * 新增
   */
  function add(isAdd) {
    title.value = '新增';
    visible.value = true;
    nextTick(() => {
      registerForm.value.add(isAdd);
    });
  }
  
  /**
   * 编辑
   * @param record
   */
  function edit(record, isAdd) {
    title.value = disableSubmit.value ? '详情' : '编辑';
    visible.value = true;
    nextTick(() => {
      registerForm.value.edit(record, isAdd);
    });
  }
  
  /**
   * 确定按钮点击事件
   */
  function handleOk() {
    registerForm.value.submitForm();
  }

  /**
   * form保存回调事件
   */
  function submitCallback() {
    handleCancel();
    emit('success');
  }

  /**
   * 取消按钮回调事件
   */
  function handleCancel() {
    visible.value = false;
  }

  defineExpose({
    add,
    edit,
    disableSubmit,
  });
</script>

<style lang="less">
  /**隐藏样式-modal确定按钮 */
  .jee-hidden {
    display: none !important;
  }
</style>
<style lang="less" scoped></style>
