<template>
  <div class="file-upload-item">
    <!-- 新增状态：显示上传区域 -->
    <div v-if="!disabled && fileList.length === 0" class="upload-area">
      <a-upload
        :accept="acceptTypes"
        :before-upload="beforeUpload"
        :show-upload-list="false"
        :multiple="false"
      >
        <div class="upload-dragger">
          <p class="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p class="ant-upload-text">仅支持excel、word、pdf格式上传</p>
        </div>
      </a-upload>
    </div>

    <!-- 编辑/详情状态：显示已上传文件 -->
    <div v-if="fileList.length > 0" class="file-list">
      <div v-for="(file, index) in fileList" :key="index" class="file-item">
        <div class="file-info">
          <span class="file-name">{{ file.name }}</span>
        </div>
        <div class="file-actions">
          <!-- 预览按钮 -->
          <a-button type="link" size="small" @click="handlePreview(file)">
            <template #icon><EyeOutlined /></template>
            预览
          </a-button>
          
          <!-- 编辑状态：显示删除和重新上传按钮 -->
          <template v-if="!disabled">
            <a-button type="link" size="small" danger @click="handleDelete(index)">
              <template #icon><DeleteOutlined /></template>
              删除
            </a-button>
            <a-upload
              :accept="acceptTypes"
              :before-upload="(file) => beforeUpload(file, index)"
              :show-upload-list="false"
              :multiple="false"
            >
              <a-button type="link" size="small">
                <template #icon><UploadOutlined /></template>
                重新上传
              </a-button>
            </a-upload>
          </template>
        </div>
      </div>
    </div>


  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { UploadOutlined, EyeOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { uploadApi } from '/@/api/sys/upload';

interface FileItem {
  name: string;
  url: string;
  uid: string;
}

const props = defineProps({
  fileList: {
    type: Array as () => FileItem[],
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:fileList']);

// 支持的文件类型
const acceptTypes = '.xlsx,.xls,.doc,.docx,.pdf';

// 文件类型验证
const validateFileType = (file: File) => {
  const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
                       'application/vnd.ms-excel',
                       'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                       'application/msword',
                       'application/pdf'];
  const allowedExtensions = ['.xlsx', '.xls', '.doc', '.docx', '.pdf'];
  
  const isValidType = allowedTypes.includes(file.type);
  const isValidExtension = allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
  
  return isValidType || isValidExtension;
};

// 上传前验证
const beforeUpload = async (file: File, replaceIndex?: number) => {
  // 文件类型验证
  if (!validateFileType(file)) {
    message.error('仅支持excel、word、pdf格式的文件！');
    return false;
  }

  // 文件大小验证（限制10MB）
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB！');
    return false;
  }

  try {
    // 调用上传API
    const response = await uploadApi({
      file,
      name: 'file',
      filename: file.name
    }, (progressEvent) => {
      // 可以在这里处理上传进度
      console.log('上传进度:', Math.round((progressEvent.loaded * 100) / progressEvent.total));
    });

    if (response && response.success) {
      const newFile: FileItem = {
        name: file.name,
        url: response.message || response.data?.url || '',
        uid: Date.now().toString()
      };

      // 更新文件列表
      const newFileList = [...props.fileList, newFile];
      emit('update:fileList', newFileList);
      
      message.success('文件上传成功！');
    } else {
      message.error('文件上传失败！');
    }
  } catch (error) {
    console.error('上传失败:', error);
    message.error('文件上传失败！');
  }

  return false; // 阻止默认上传行为
};

// 预览文件
const handlePreview = (file: FileItem) => {
  if (file.url) {
    window.open(file.url, '_blank');
  } else {
    message.warning('文件链接不存在！');
  }
};

// 删除文件
const handleDelete = (index: number) => {
  const newFileList = [...props.fileList];
  newFileList.splice(index, 1);
  emit('update:fileList', newFileList);
  message.success('文件删除成功！');
};
</script>

<style lang="less" scoped>
.file-upload-item {
  .upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    text-align: center;
    padding: 20px;
    cursor: pointer;
    transition: border-color 0.3s;

    &:hover {
      border-color: #1890ff;
    }

    .upload-dragger {
      .ant-upload-drag-icon {
        font-size: 24px;
        color: #999;
        margin-bottom: 8px;
      }

      .ant-upload-text {
        color: #666;
        font-size: 14px;
        margin: 0;
      }
    }
  }

  .upload-area-small {
    margin-top: 8px;
  }

  .file-list {
    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      margin-bottom: 8px;
      background: #fff;

      .file-info {
        flex: 1;
        
        .file-name {
          color: #333;
          font-size: 14px;
        }
      }

      .file-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
}
</style>
