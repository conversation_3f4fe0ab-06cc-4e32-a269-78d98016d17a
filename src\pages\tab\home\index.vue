<template>
  <view>
    <!-- <up-navbar title="练习" leftIcon=""></up-navbar> -->
    <!-- <view class="custom-navbar">
            <image src="/static/images/index-bg.png" class="background-image" />
            <view class="title">练习</view>>
        </view> -->
    <!-- 轮播图 -->
    <view class="flex">
      <image :src="bannerImg" mode="widthFix" class="w-full" />
    </view>
    <!-- <up-swiper indicator indicatorMode="line" :radius="0" circular :list="list1"></up-swiper> -->
    <!-- <view class="swiper-box">
      <image src="/static/images/index-bg.png" class="index-bg" />
      <view class="swiper">
      </view>
    </view> -->
    <!-- 我的练习 -->

    <view class="practice">
      <view class="flex items-center justify-between">
        <view class="title">
          我的练习
        </view>
        <up-icon name="arrow-right" />
      </view>
      <view>
        <view
          v-for="(item, index) in practiceList" :key="index"
          class="mt-20rpx flex items-center rd-16rpx bg-white pa-30rpx" @click="goDetail(item)"
        >
          <up-image src="/static/images/index-list.png" width="100rpx" height="100rpx" shape="circle" />
          <view class="ml-25rpx">
            <view class="text-34rpx text-#222 font-500">
              {{
                item.planName
              }}
            </view>
            <view class="mt-30rpx text-20rpx text-#666">
              {{ item.startTime }} ~ {{ item.endTime }}
            </view>
          </view>
        </view>
        <noData v-if="!!!practiceList?.length" />
        <!-- <up-loadmore :status="loadStatus" /> -->
      </view>
    </view>
    <!-- #ifdef MP-WEIXIN -->
    <!-- 隐私协议组件 -->
    <agree-privacy v-model="showAgreePrivacy" :disable-check-privacy="false" @agree="handleAgree" />
    <!-- #endif -->
  </view>
</template>

<script setup lang="ts">
import { HomeApi } from '@/api';
import { timestampToStandardTime } from '@/utils';
// import noData from "./../../../../src/components/no-data/no-data.vue";
import { useUserStore } from '@/store';
import bannerImg from '@/static/images/banner.jpg';
// 使用 reactive 创建响应式数组
// const list1 = reactive([bannerImg]);

const title = ref<string>();
title.value = import.meta.env.VITE_APP_TITLE;

const store = useUserStore();

const showAgreePrivacy = ref(false);
// 同意隐私协议
function handleAgree() {
  console.log('同意隐私政策');
}
const practiceList = ref<any>([]);
const pageNo = ref(1);
const pageSize = ref(10);
const getList = async () => {
  const params = {
    pageNo: pageNo.value,
    pageSize: pageSize.value,
  };
  const res = await HomeApi.getPracticeList(params);
  res.data.list.forEach((item: any) => {
    item.startTime = timestampToStandardTime(item.exercisesTime[0]);
    item.endTime = timestampToStandardTime(item.exercisesTime[1]);
  });
  if (res.data.list.length > 0) {
    practiceList.value = [...practiceList.value, ...res.data.list];
  }
};
const goDetail = (item: any) => {
  uni.navigateTo({
    url: `/pages/tab/planDetail/index?id=${item.exercisesPlanId}`,
  });
};

onReachBottom(() => {
  pageNo.value++;
  getList();
});
onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
  .u-card-wrap {
    padding: 1px;
    background-color: $u-bg-color;
  }

  .u-body-item {
    padding: 20rpx 0rpx;
    font-size: 32rpx;
    color: #333;
  }

  .u-body-item image {
    margin-left: 12rpx;
    width: 120rpx;
    height: 120rpx;
    border-radius: 8rpx;
    flex: 0 0 120rpx;
  }

  // .custom-navbar {
  //     width: 100%;
  //     height: 428rpx;
  //     position: relative;
  //     text-align: center;
  //     .background-image {
  //         position: absolute;
  //         left: 0;
  //         top: 0;
  //         bottom: 0;
  //         right: 0;
  //         width: 100%;
  //         height: 100%;
  //         z-index: -1;
  //         object-fit: cover;
  //     }
  //     .title{
  //         color: #fff;
  //         position: absolute;
  //         z-index: 99;
  //         top: 25%;
  //         left: 45%;
  //         font-size: 36rpx;
  //     }
  // }

  .swiper-box {
    position: relative;
    padding: 0 30rpx;
    height: 428rpx;

    .index-bg {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 0;
      width: 100%;
      height: 100%;
    }

    .swiper {
      position: absolute;
      bottom: -60rpx;
      z-index: 1;
      margin: 0 auto;
      width: 90%;
      border-radius: 20rpx;
    }
  }

  .practice {
    padding: 0 30rpx;
    margin-top: 100rpx;

    .title {
      font-size: 38rpx;
      color: #222;
      font-weight: 600;
    }
  }
</style>
