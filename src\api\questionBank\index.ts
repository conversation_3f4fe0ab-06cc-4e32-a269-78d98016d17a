/**
 * 题库练习配置相关接口
 */
import type { detailParams, treeByLevelParams } from './types'
import { get } from '@/utils/request'
import type { CommonResult } from '@/api/common/types'

enum URL {
    statisticsDetail = 'question/exercises-library-statistics/weChatMiniProgram/findStatisticsDetail',
    treeByLevel = 'question/exercises-knowledge-tree/weChatMiniProgram/queryTreeByLevel',
}
export const findStatisticsDetail = (params: detailParams) => get<CommonResult>({ url: URL.statisticsDetail, params })
export const getTreeByLevel = (params: treeByLevelParams) => get<CommonResult>({ url: URL.treeByLevel, params })
