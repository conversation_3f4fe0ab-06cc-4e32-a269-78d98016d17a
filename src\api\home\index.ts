/**
 * 首页配置相关接口
 */
import type { listParams, detailParams } from './types'
import { get } from '@/utils/request'
import type { CommonResult } from '@/api/common/types'

enum URL {
    practiceList = 'question/exercises-plan/weChatMiniProgram/page',
    practiceDetail = 'question/exercises-plan/weChatMiniProgram/info',
}
export const getPracticeList = (params: listParams) => get<CommonResult>({ url: URL.practiceList, params })
export const getPracticeDetail = (params: detailParams) => get<CommonResult>({ url: URL.practiceDetail, params })
