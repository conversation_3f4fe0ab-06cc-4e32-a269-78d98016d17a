/**
 * 我的配置相关接口
 */
import type { touchParams } from './types';
import { get,put } from '@/utils/request';
import type { CommonResult } from '@/api/common/types';

enum URL {
	activateList = 'question/exercises-myself-config/weChatMiniProgram/activateList',
	touch = 'question/exercises-myself-config/weChatMiniProgram/touch',
}
export const getActivateList = () => get<CommonResult>({ url: URL.activateList });
export const touchConfig = (data : touchParams) => put<CommonResult>({ url: URL.touch, data });