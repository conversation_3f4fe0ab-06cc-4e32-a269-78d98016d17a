<template>
  <view class="pa-30rpx">
    <view class="bg-white pa-30rpx header rd-12rpx">
      <view class="text-#333 text-36rpx font-600">{{
        questionBankData.planName
      }}</view>
      <view class="text-#999 text-24rpx">职业等级：{{ questionBankData.careerName }}
      </view>
      <view class="text-#999 text-24rpx">所属科目：{{ ruleMap[questionBankData.ruleType] }}</view>
      <view class="text-#999 text-24rpx"
        v-if="questionBankData.exercisesTime && questionBankData.exercisesTime.length > 0">练习时间：{{
          timestampToStandardTime(questionBankData.exercisesTime[0])
        }}
        ~ {{ timestampToStandardTime(questionBankData.exercisesTime[1]) }}</view>
    </view>
    <view>
      <view class="text-#222 text-32rpx font-700 my-42rpx">模拟练题</view>
      <view class="bg-white">
        <u-cell-group :border="false" :customStyle="{ borderRadius: '12rpx' }">
          <u-cell v-for="(
              item, index
            ) in questionBankData.miniExerciseLibraryQuestionResp" :key="index" is-link :icon="item.icon"
            :title="item.questionName" :name="item.id" :titleStyle="{ marginLeft: '20rpx' }" :border="
              index ==
              questionBankData.miniExerciseLibraryQuestionResp.length - 1
                ? false
                : true
            " @click="goQuestionBank(item)">
          </u-cell>
        </u-cell-group>
      </view>
    </view>
    <view>
      <view class="text-#222 text-32rpx font-700 my-42rpx">模拟测试</view>
      <view class="bg-white">
        <u-cell-group :border="false" :customStyle="{ borderRadius: '12rpx' }">
          <u-cell v-for="(item, index) in questionBankData.miniExerciseQuestionResp" :key="index" is-link
            :icon="item.icon" :title="item.paperName" :name="item.id" :titleStyle="{ marginLeft: '20rpx' }" :border="
              index == questionBankData.miniExerciseQuestionResp.length - 1
                ? false
                : true
            " @click="goMockTest(item)">
          </u-cell>
        </u-cell-group>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { HomeApi } from "@/api";
  import { timestampToStandardTime } from "@/utils";
  const questionBankData = ref<any>({});
  const exercisesPlanId = ref();
  const ruleMap : any = {
    10: "理论",
    20: "技能",
  };
  const getPracticeDetail = async () => {
    const res = await HomeApi.getPracticeDetail({ id: exercisesPlanId.value });
    questionBankData.value = res.data;
  };
  const goQuestionBank = (item : any) => {
    uni.navigateTo({
      url: `/pages/tab/questionBank/index?exercisesPlanId=${exercisesPlanId.value}&questionLibraryId=${item.id}&questionName=${item.questionName}&careerCode=${questionBankData.value.careerCode}`,
    });
  };

  onMounted(() => {
    const { proxy } = getCurrentInstance();
    const options = proxy.options || proxy.$scope.options;
    console.log(options, 'personalInfo')
    exercisesPlanId.value = options.id;
    getPracticeDetail();
  });

  const goMockTest = (item : any) => {
    console.log(6543, item)

    uni.navigateTo({
      url: `/pages/tab/mockTest/index?exercisesPlanId=${exercisesPlanId.value}&paperId=${item.id}&paperName=${item.paperName}`,
    });

  };
</script>

<style scoped lang="scss">
  .header {
    view {
      margin-top: 20rpx;
    }
  }
</style>
