<template>
  <view class="exam-box bg-white" v-if="Object.keys(paperInfo).length > 0">
    <!-- <up-radio-group v-model="value">
      <up-radio activeColor="red" labelColor="red" label="思悠悠，恨悠悠，恨到归时方始休"></up-radio>
    </up-radio-group> -->

    <view>
      <view class="flex items-center pa-30rpx bg-white header">
        <up-icon name="list-dot" color="#4076F6" size="24" @click="showTree"></up-icon>
        <text class="text-#333 text-28rpx ml-8rpx">知识点:{{ paperInfo.parentKnowledgeTreeName }}</text>
      </view>
      <view class="px-30rpx py-36rpx">
        <view>
          <span class="quesiton-type">{{
            questionType[paperInfo.questionTypeCode]
          }}</span>
          <text class="text-32rpx text-#333">{{ paperInfo.sequenceNum }}、{{ paperInfo.stem }}</text>
        </view>
        <view class="pl-30rpx">

          <up-radio-group class="quesition-group" v-model="paperInfo.answerExercisesQuestion" labelSize="32rpx"
            labelColor="#333" placement="column" :class="(answerStatus !== 10 || requestType == 3)&& 'not-working'"
            v-if="(paperInfo.questionTypeCode === 'B' ||
              paperInfo.questionTypeCode === 'C')
            ">

            <view v-for="(item,index) in paperInfo.checkOption" :key="index.toString().slice(-1)">
              <up-radio :customStyle="{ marginTop: '48rpx' }" :label="index.toString().slice(-1) + '、' + item"
                :name="index.toString().slice(-1)" :activeColor="resultBC(index.toString().slice(-1))"
                :labelColor="resultBC(index.toString().slice(-1))" v-if="item">
              </up-radio>
            </view>
          </up-radio-group>

          <!--  :disabled="answerStatus !== 10 || requestType == 3" -->
          <up-checkbox-group class="quesition-group" v-model="paperInfo.answerExercisesQuestion" placement="column"
            labelSize="32rpx" labelColor="#333" :class="(answerStatus !== 10 || requestType == 3)&& 'not-working'"
            v-if="paperInfo.questionTypeCode === 'D'">
            <view v-for="(item, index) in paperInfo.checkOption" :key="index.toString().slice(-1)">

              <up-checkbox :customStyle="{ marginTop: '48rpx' }" :label="index.toString().slice(-1) + '、' + item"
                :name="index.toString().slice(-1)" :activeColor="resultD(index.toString().slice(-1))"
                :labelColor="resultD(index.toString().slice(-1))" v-if="item">
              </up-checkbox>
            </view>
          </up-checkbox-group>

        </view>
        <view v-if="analysisShow" class="mt-85rpx text-#333 text-28rpx">
          <view class="text-align-center flex items-center justify-around mb-48rpx"><up-line color="#EEEEEE"
              length="30%"></up-line><text>试题解析</text><up-line color="#EEEEEE" length="30%"></up-line></view>
          <view class="left-border">正确答案<text class="ml-20rpx text-#4076F6">{{
              paperInfo.questionTypeCode === "D"
                ? paperInfo.paperAnswer.split("").join("、")
                : paperInfo.paperAnswer
            }}</text></view>
          <view class="left-border my-48rpx" v-if="requestType != 3">你的答案<text :class="[
                'ml-20rpx',
                answerStatus == 30 ? 'text-#FE474A' : 'text-#4076F6',
              ]">{{
                paperInfo.questionTypeCode === "D"
                  ? paperInfo.answerExercisesQuestion.length
                    ? paperInfo.answerExercisesQuestion.join("、")
                    : ""
                  : paperInfo.answerExercisesQuestion
              }}</text></view>

          <view class="left-border my-48rpx text-#999">试题解析
            <view class="mt-16rpx text-#333">{{
              paperInfo.paperQuestionAnalysis
            }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="flex justify-between border-t-1px border-t-solid border-t-#E5E5E5 pt-20rpx px-30rpx py-36rpx"
      v-if="paperInfo && paperInfo.libraryItemPointInfo">
      <view class="flex items-center">
        <view class="text-36rpx"><text class="text-#333">{{ paperInfo.sequenceNum }}</text><text
            class="text-#999">/{{ paperInfo.libraryItemPointInfo.questionTotal }}</text></view>
        <view class="mx-40rpx">
          <up-icon :name="
              paperInfo.libraryItemPointInfo.collectFlag ? 'star-fill' : 'star'
            " :label="
              paperInfo.libraryItemPointInfo.collectFlag ? '取消收藏' : '收藏'
            " size="44rpx" labelPos="bottom" labelSize="24rpx" labelColor="#999"
            :color="paperInfo.libraryItemPointInfo.collectFlag && 'orange'" @click="handleCollect(paperInfo)"></up-icon>
        </view>
      </view>
      <view class="flex items-center">
        <up-button type="primary" text="上一题" shape="circle" plain :customStyle="{
            marginRight: '20rpx',
            width: '146rpx',
            height: '54rpx',
          }" @click="
            getNextQuestion(
              paperInfo.libraryItemPointInfo.sequenceNum,
              10,
              paperInfo.id,
              paperInfo.parentKnowledgeTreeId
            )
          " :disabled="paperInfo.libraryItemPointInfo.sequenceNum === 1"></up-button>
        <up-button type="primary" text="下一题" shape="circle" :customStyle="{ width: '146rpx', height: '54rpx' }" @click="
            getNextQuestion(
              paperInfo.libraryItemPointInfo.sequenceNum,
              20,
              paperInfo.id,
              paperInfo.parentKnowledgeTreeId
            )
          "></up-button>
      </view>
    </view>
  </view>
  <view v-if="Object.keys(paperInfo).length == 0 && requestType == 1">
    <view class="no-empty bg-white">
      <image src="/static/images/no-collect.png"></image>
    </view>
  </view>
  <view v-if="Object.keys(paperInfo).length == 0 && requestType == 2">
    <view class="no-empty bg-white">
      <image src="/static/images/no-wrong.png"></image>
      <view>暂无错题</view>
    </view>
  </view>
  <up-popup :show="popupShow" mode="left" @close="close" :customStyle="{ width: '300px' }">
    <da-tree ref="DaTreeRef" :data="treeData" labelField="name" valueField="id" childrenField="childNodeList"
      :showRadioIcon="false" :showTotal="false" :defaultExpandedKeys="defaultExpandedKeys"
      :defaultCheckedKeys="defaultCheckedKeys" @change="handleTreeChange"></da-tree>
  </up-popup>
</template>

<script setup lang="ts">
  import { BrushApi } from "@/api";
  const exercisesPlanId = ref(); //计划id
  const questionLibraryId = ref(); //题库id
  const libraryItemId = ref(); //知识树id
  const currentTreeId = ref(); //当前树
  const questionName = ref();
  const requestType = ref(); //请求类型
  const careerCode = ref(); //职业工种等级编码
  const analysisShow = ref(false);
  const popupShow = ref(false);
  const answerStatus = ref(10); // 10未答 20正确 30错误
  const questionTotal = ref();
  const noDataSrc = {
    1: "/static/images/no-collect.png",
    2: "/static/images/no-wrong.png",
  };
  const titleMap = {
    1: "我的收藏",
    2: "我的错题",
    3: "背题模式",
  };
  const questionType : any = {
    B: "单选题",
    D: "多选题",
    C: "判断题",
    E: "简答题",
    F: "论述题",
  };
  const paperInfo = ref<any>({});
  const treeData = ref([]);
  const DaTreeRef = ref();
  const defaultExpandedKeys = ref([]);
  const defaultCheckedKeys = ref();



  let resultBC = computed(() => (item) => {
    if (answerStatus.value !== 10 || requestType.value == 3) {
      if (item == paperInfo.value.answerExercisesQuestion) {
        return paperInfo.value.answerExercisesQuestion === paperInfo.value.paperAnswer
          ? "#4076F6"
          : "#FE474A";
      }
    }
    return ""
  })


  let resultD = computed(() => (item) => {
    let color : any = ''
    if (answerStatus.value !== 10 || requestType.value == 3) {
      if (paperInfo.value.answerExercisesQuestion?.includes(item)) {
        color = '#FE474A';
        let bol = paperInfo.value.paperAnswer?.split('').some(itemr => itemr == item)
        if (!!bol) {
          color = '#4076F6';
        }
      }
      return color
    }
    return ""
  })

  const showTree = () => {
    getTreeAll();
    // DaTreeRef.value.setExpandedKeys([paperInfo.value.parentKnowledgeTreeId], true);
    // DaTreeRef.value.setCheckedKeys([paperInfo.value.parentKnowledgeTreeId], true);
  };
  const handleTreeChange = (values, currentItem) => {
    // 支持修改节点数据

    const item = currentItem.originItem;
    getQuestion(item.knowledgeTreePointInfo.sequenceNum, 20, values);
    popupShow.value = false;
  };

  //通过题目id查询题目
  const getQuestionById = async (libraryId) => {
    const params = {
      libraryItemId: libraryId,
      requestType: requestType.value,
    };
    const res = await BrushApi.findLibraryItemByRequestType(params);
    paperInfo.value = res.data;
    answerStatus.value = res.data.answerStatus;

    if (requestType.value == 3) {
      paperInfo.value.answerExercisesQuestion = paperInfo.value.paperAnswer;
      analysisShow.value = true;
    }

    if (res.data.questionTypeCode === "D") {
      paperInfo.value.answerExercisesQuestion = res.data.answerExercisesQuestion.split(',')
    }

    if (answerStatus.value != 10) {
      analysisShow.value = true;
    }


    if (requestType.value == 4) {
      uni.setNavigationBarTitle({
        title: paperInfo.value.parentKnowledgeTreeName,
      });
    }
  };
  //查询上一题下一题
  const getNextQuestion = async (
    sequenceNum,
    operateType,
    id,
    parentKnowledgeTreeId,
  ) => {
    if (id) {

      if (operateType == 20 && !analysisShow.value && requestType.value != 3) {
        //下一题之前校验答案 展示解析
        if (!paperInfo.value.answerExercisesQuestion) {
          return uni.showToast({
            title: "请作答后在进入下一题",
            icon: "none",
          });
        }
        const params = {
          questionExercisesLibraryItemId: id,
          requestType: requestType.value,
          answerExamQuestion:
            paperInfo.value.questionTypeCode === "D"
              ? paperInfo.value.answerExercisesQuestion.join(",")
              : paperInfo.value.answerExercisesQuestion,
        };
        const res = await BrushApi.checkLibraryStageSubmit(params);
        if (res.code === 0) {
          answerStatus.value = res.data.answerStatus;
          analysisShow.value = true;
        }
      } else {
        getQuestion(sequenceNum, operateType, parentKnowledgeTreeId);
      }
    } else {
      //第一次进入
      console.log('第一次进入')
      getQuestion(sequenceNum, operateType);
    }
  };
  const getQuestion = async (sequenceNum, operateType, parentKnowledgeTreeId) => {
    uni.showLoading({
      title: "加载中",
    });
    const params = {
      exercisesPlanId: exercisesPlanId.value,
      questionLibraryId: questionLibraryId.value,
      sequenceNum: sequenceNum,
      operateType: operateType,
      requestType: requestType.value,
      parentKnowledgeTreeId: parentKnowledgeTreeId ? parentKnowledgeTreeId : null,
      currentTreeId: currentTreeId.value,
    };
    const res = await BrushApi.findNextLibraryItem(params);
    if (res && res.code == 0) {

      uni.hideLoading();
      paperInfo.value = res.data;
      answerStatus.value = res.data.answerStatus;

      // 设置url
      const queryInfo = {
        exercisesPlanId: paperInfo.value.exercisesPlanId,
        questionLibraryId: paperInfo.value.questionLibraryId,
        requestType: paperInfo.value.libraryItemPointInfo
          .requestType,
        libraryItemId: paperInfo.value.id,
        careerCode: paperInfo.value.careerCode,
        questionName: questionName.value
      };
      const newUrl = `${window.location.protocol}//${window.location.host}${window.location.pathname}?queryInfo=${JSON.stringify(queryInfo)}`;
      window.history.pushState({}, '', newUrl);

      if (requestType.value == 3) {
        paperInfo.value.answerExercisesQuestion = paperInfo.value.paperAnswer;
        analysisShow.value = true;
      } else {

        if (operateType == 20 && answerStatus.value == 10) {
          analysisShow.value = false;

        } else if (operateType == 10 || answerStatus.value == 30 || answerStatus.value == 20) {
          analysisShow.value = true;
        }
      }



      console.log(answerStatus.value, analysisShow.value, 'analysisShow.value ')
      paperInfo.value.result = {};
      if (res.data.questionTypeCode === "D") {
        if (requestType.value == 3) {
          paperInfo.value.answerExercisesQuestion = paperInfo.value.answerExercisesQuestion?.split('');
        } else {
          paperInfo.value.answerExercisesQuestion = paperInfo.value.answerExercisesQuestion?.split(',');
        }

      }
      if (requestType.value == 4) {
        uni.setNavigationBarTitle({
          title: paperInfo.value.parentKnowledgeTreeName,
        });
      }


    } else {
      if (requestType.value == 1) {
        //取消收藏且一道收藏题都没有的时候返回-20 展示无数据列表  请求封装res为undefined所用题目总数判断
        if (questionTotal.value == 1) {
          //为1说明取消收藏最后一题成功 列表为空
          paperInfo.value = {};
        }
      } else {

        if (paperInfo.value.libraryItemPointInfo.lastFlag) {
          uni.showModal({
            title: '提示', // 模态框标题
            content: '当前类目所有试题已答完，是否进入下一类目?', // 模态框内容
            showCancel: true, // 是否显示取消按钮，默认为true
            cancelText: '取消', // 取消按钮的文字
            confirmText: '确定', // 确定按钮的文字
            success(res) {
              if (res.confirm) {
                jumpNext()
                // 执行确认后的逻辑
              } else if (res.cancel) {
                // 执行取消后的逻辑
              }
            },
          });
        }
      }
    }
  };
  const jumpNext = async () => {
    const params = {
      exercisesPlanId: exercisesPlanId.value,
      questionLibraryId: questionLibraryId.value,
      requestType: requestType.value,
      operateType: 20,
      treeId: paperInfo.value.parentKnowledgeTreeId,
      // lastItemId: paperInfo.value.lastItemId,
    };

    const res = await BrushApi.findAdjacentTree(params);
    if (res && res.code == 0) {
      if (res.data.lastItemId) {
        getQuestionById(res.data.lastItemId)
      } else {
        getQuestion(0, 20, res.data.treeId);
      }

    }
  }
  //获取知识树
  const getTreeAll = async () => {
    uni.showLoading({
      title: "加载中",
    });
    const params = {
      exercisesPlanId: exercisesPlanId.value,
      questionLibraryId: questionLibraryId.value,
      requestType: requestType.value,
      careerCode: careerCode.value,
    };
    const res = await BrushApi.queryAllTree(params);
    uni.hideLoading();
    treeData.value = res.data;
    popupShow.value = true;
    defaultCheckedKeys.value = paperInfo.value.parentKnowledgeTreeId;
    defaultExpandedKeys.value = [paperInfo.value.parentKnowledgeTreeId];
  };
  const close = () => {
    popupShow.value = false;
  };
  //收藏 取消收藏
  const handleCollect = async (item) => {
    console.log(paperInfo.value.answerExercisesQuestion, 'paperInfo.value.answerExercisesQuestionpaperInfo.value.answerExercisesQuestion')
    const params = {
      exercisesPlanId: exercisesPlanId.value,
      questionLibraryId: questionLibraryId.value,
      questionExercisesLibraryItemId: paperInfo.value.id,
      answerExercisesQuestion:
        paperInfo.value.questionTypeCode === "D"
          ? paperInfo.value.answerExercisesQuestion.join("")
          : paperInfo.value.answerExercisesQuestion,
    };
    const res = await BrushApi.saveOrUpdateCollection(params);
    if (res && res.code == 0) {
      if (res.data == 20) {
        //取消收藏 在我的收藏页面需要重新查数据
        uni.showToast({
          title: "取消收藏成功",
          icon: "success",
        });

        //收藏模式下 取消收藏
        if (item.libraryItemPointInfo.requestType == 1) {
          //收藏模式下通过题目总数判断取消成功后有没有试题
          questionTotal.value = item.libraryItemPointInfo.questionTotal;
          if (item.libraryItemPointInfo.sequenceNum == 1) {
            getNextQuestion(0, 20, null, item.parentKnowledgeTreeId);
          } else {
            getNextQuestion(
              item.libraryItemPointInfo.sequenceNum,
              10,
              null,
              item.parentKnowledgeTreeId
            );
          }
        } else {
          //其他模式
          getQuestionById(item.id); //取消收藏成功 刷新页面获取最新数据
        }
      } else {
        uni.showToast({
          title: "收藏成功",
          icon: "success",
        });
        //收藏成功 刷新页面获取最新数据
        getQuestionById(item.id);
      }
    }
  };
  onMounted(() => {
    const { proxy } = getCurrentInstance();
    const options = proxy.options || proxy.$scope.options;
    const queryInfo = JSON.parse(options.queryInfo);

    console.log(queryInfo, 'queryInfoqueryInfoqueryInfo')
    careerCode.value = queryInfo.careerCode;
    if (queryInfo.requestType != 4) {
      uni.setNavigationBarTitle({
        title: titleMap[queryInfo.requestType],
      });
    }

    exercisesPlanId.value = queryInfo.exercisesPlanId;
    questionLibraryId.value = queryInfo.questionLibraryId;
    requestType.value = queryInfo.requestType;
    libraryItemId.value = queryInfo.libraryItemId;
    questionName.value = queryInfo.questionName;
    currentTreeId.value = queryInfo.currentTreeId;

    console.log(queryInfo.libraryItemId, 'queryInfo.libraryItemId')
    if (queryInfo.libraryItemId) {
      //已经存在id  不是第一次 通过id查询题目明细
      getQuestionById(queryInfo.libraryItemId);
    } else {
      //从知识树进入 根据树id查询题目
      console.log(queryInfo.hasOwnProperty('parentKnowledgeTreeId'), 'queryInfo.hasOwnProperty(')
      // if (queryInfo.hasOwnProperty('parentKnowledgeTreeId')) {
      //   const sequenceNum = queryInfo.sequenceNum;
      //   const operateType = 20;
      //   const parentKnowledgeTreeId = queryInfo.parentKnowledgeTreeId;
      //   // getQuestion(sequenceNum, operateType, parentKnowledgeTreeId);
      //   getQuestionById(queryInfo.lastLibraryItemId);
      // } else {
      //   console.log('第一次进入题库 查询默认第一题')
      //   //第一次进入题库 查询默认第一题
      //   const sequenceNum = 0;
      //   const operateType = 20;
      //   getNextQuestion(sequenceNum, operateType);
      //   // getQuestionById(queryInfo.lastLibraryItemId);
      // }

      // 最后一层
      if (queryInfo.lastTreeFlag) {
        const sequenceNum = queryInfo.sequenceNum;
        const operateType = 20;
        const parentKnowledgeTreeId = queryInfo.parentKnowledgeTreeId;
        getQuestion(sequenceNum, operateType, parentKnowledgeTreeId);


      } else {

        if (queryInfo.lastLibraryItemId) {
          getQuestionById(queryInfo.lastLibraryItemId);
        } else {
          console.log('第一次进入题库 查询默认第一题')
          //第一次进入题库 查询默认第一题
          const sequenceNum = 0;
          const operateType = 20;
          getNextQuestion(sequenceNum, operateType);
        }
      }



      // if (queryInfo.hasOwnProperty('parentKnowledgeTreeId')) {
      //   const sequenceNum = queryInfo.sequenceNum;
      //   const operateType = 20;
      //   const parentKnowledgeTreeId = queryInfo.parentKnowledgeTreeId;
      //   getQuestion(sequenceNum, operateType, parentKnowledgeTreeId);

      // } else {

      //   if (queryInfo.lastLibraryItemId) {
      //     getQuestionById(queryInfo.lastLibraryItemId);
      //   } else {
      //     console.log('第一次进入题库 查询默认第一题')
      //     //第一次进入题库 查询默认第一题
      //     const sequenceNum = 0;
      //     const operateType = 20;
      //     getNextQuestion(sequenceNum, operateType);
      //   }
      // }
    }
  });
  onBackPress((options) => {
    uni.navigateTo({
      url: `/pages/tab/questionBank/index?exercisesPlanId=${exercisesPlanId.value}&questionName=${questionName.value}&careerCode=${paperInfo.value.careerCode}&questionLibraryId=${questionLibraryId.value}`,
    });
    return true;
  })
</script>

<style scoped lang="scss">
  .header {
    box-shadow: 0rpx 0rpx 14rpx 0rpx rgba(0, 0, 0, 0.14);
  }

  .exam-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: calc(100vh - 44px);
    box-sizing: border-box;

    .quesiton-type {
      display: inline-block;
      width: 90rpx;
      height: 38rpx;
      line-height: 38rpx;
      background: url("/static/images/mocktest/type-bg.png") no-repeat center;
      background-size: contain;
      font-size: 20rpx;
      padding-left: 16rpx;
      margin-right: 2rpx;
    }

    .quesition-group {
      .u-radio {
        display: flex;
        align-items: flex-start;

        :deep(uni-text.u-radio__text) {
          flex: 1;
        }
      }
    }

    .left-border {
      position: relative;

      &::before {
        content: "";
        position: absolute;
        left: -30rpx;
        top: 6rpx;
        width: 8rpx;
        height: 28rpx;
        background: linear-gradient(180deg, #74aefb 0%, #4076f6 100%);
        border-radius: 0rpx 6rpx 6rpx 0rpx;
      }
    }
  }

  .no-empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    /* 垂直居中 */
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  .not-working {
    user-select: none;
    pointer-events: none;
  }
</style>
