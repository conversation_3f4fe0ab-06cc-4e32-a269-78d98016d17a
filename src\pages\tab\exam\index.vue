<template>
  <view class="exam-box h-full bg-white">
    <view class="content px-30rpx py-36rpx">
      <view>
        <QuestionType
          v-if="showQuestion"
          v-model="answer"
          :paper-info="paperInfo"
          :request-type="queryInfo.requestType"
        />
        <analysis
          v-if="showAnalysis"
          :paper-info="paperInfo"
          :request-type="queryInfo.requestType"
        />
      </view>
    </view>
    <FooterBar
      v-if="showFooter"
      :paper-info="paperInfo"
      :query-info="queryInfo"
      :answer="answer"
      :paper-type="paperType.exam"
      @handle-click-up="handleClickUp"
      @handle-click-down="handleClickDown"
      @handle-click-submit="handleClickSubmit"
      @handle-show-answer-card="handleShowAnswerCard"
    />
  </view>
  <up-popup :show="popupShow" mode="bottom" round="12rpx" :custom-style="{ minHeight: '30%', maxHeight: '50%' }" @close="close">
    <AnswerCard
      v-if="popupShow && showAnswerCard"
      :paper-info="paperInfo"
      :query-info="queryInfo"
      @handle-click-answer-card="handleClickAnswerCard"
    />
  </up-popup>
</template>

<script setup lang="ts">
import { getrRouteQueryData, isEmptyObject } from '@/utils/common';
import { ExamApi } from '@/api';

import QuestionType from '@/pages/tab/components/question/index.vue';
import analysis from '@/pages/tab/components/analysis/index.vue';
import AnswerCard from '@/pages/tab/components/answerCard/index.vue';
import FooterBar from '@/pages/tab/components/footer/index.vue';
//  路由参数
interface QueryInfo {
  exercisesPlanId: number; // 计划id
  paperId: number;
  paperItemPointInfo: {
    lastFlag: boolean;
    lastPaperItemId: number;
    questionTotal: number;
    sequenceNum: number;
  };
  paperName: string;
  questionExercisesPaperStatisticsId: number;
  token: string;
  requestType: requestType;
}
enum paperType {
  brush,
  exam,
}
enum requestType {
  test = 5,
  detail = 6,
}
const queryInfo: Ref<QueryInfo> = ref({
  exercisesPlanId: 0, // 计划id
  paperId: 0,
  paperItemPointInfo: {
    lastFlag: false,
    lastPaperItemId: 0,
    questionTotal: 0,
    sequenceNum: 0,
  },
  paperName: '',
  questionExercisesPaperStatisticsId: 0,
  token: '',
  requestType: 5,
});
const paperInfo: Ref<any> = ref({}); // 试卷信息
const showAnalysis = computed(
  () =>
    !isEmptyObject(paperInfo.value)
    && queryInfo.value.requestType === requestType.detail,
);
const showFooter = computed(() => !isEmptyObject(paperInfo.value));
const showQuestion = computed(() => !isEmptyObject(paperInfo.value));
const showAnswerCard = computed(() => !isEmptyObject(paperInfo.value));
const answer = ref('');

const enterPaper = async () => {
  const { requestType } = queryInfo.value;
  const {
    questionExercisesPaperStatisticsId,
    exercisesPlanId,
    paperId,
    paperItemPointInfo,
  } = queryInfo.value;
  const { lastPaperItemId } = paperItemPointInfo;

  if (requestType === 5 && lastPaperItemId) {
    const { data } = await ExamApi.findItemById({
      id: lastPaperItemId,
    });
    paperInfo.value = data;
  }
  else {
    const { data } = await ExamApi.findNextItem({
      questionExercisesPaperStatisticsId,
      exercisesPlanId,
      paperId,
      sequenceNum: 0,
      operateType: 20,
    });
    paperInfo.value = data;
  }
};
const handleClickUp = (data: any) => {
  paperInfo.value = data;
};
const handleClickDown = (data: any) => {
  paperInfo.value = data;
};
const handleClickSubmit = (data: any) => {
  console.log(data, 'handleClickSubmit');
  uni.navigateTo({
    url: `/pages/tab/submit/index?queryInfo=${JSON.stringify(data)}`,
  });
};
const popupShow = ref(false);
const handleShowAnswerCard = async () => {
  console.log('展示答题卡');
  popupShow.value = true;
};
// 关闭知识树
const close = () => {
  console.log('关闭答题卡');
  popupShow.value = false;
};
// 知识树点击
const handleClickAnswerCard = async (id: any) => {
  paperInfo.value = {};
  const { data } = await ExamApi.findItemById({
    id,
  });
  paperInfo.value = data;
  close();
};

onMounted(async () => {
  // 获取路由参数
  queryInfo.value = getrRouteQueryData();
  console.log(queryInfo.value, 'queryInfo');
  uni.setNavigationBarTitle({
    title: queryInfo.value.paperName,
  });
  await enterPaper();
});
</script>

<style scoped lang="scss">
.exam-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  box-sizing: border-box;
}

.header {
  box-shadow: 0rpx 0rpx 14rpx 0rpx rgb(0 0 0 / 14%);
}

.content {
  flex: 1;
  overflow: scroll;
}

.footer {
  height: 50rpx;
}

.no-empty {
  /* 垂直居中 */
  position: absolute;
  inset: 0;
  display: flex;
  justify-content: center;

  /* 水平居中 */
  align-items: center;
  flex-direction: column;
}

.not-working {
  user-select: none;
  pointer-events: none;
}
</style>
