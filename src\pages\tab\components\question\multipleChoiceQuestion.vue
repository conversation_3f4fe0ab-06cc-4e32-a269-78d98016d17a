<template>
  <view>
    <view>
      <span class="quesiton-type">多选题</span>
      <text class="text-32rpx text-#333">
        {{ questionInfo.sequenceNum }}、{{ questionInfo.question }}
      </text>
    </view>
    <up-checkbox-group
      v-model="answer"
      class="quesition-group"
      label-size="32rpx"
      label-color="#333"
      placement="column"
    >
      <view
        :class="{
          'not-working': props.questionInfo.status === Status.feedback,
        }"
      >
        <up-checkbox
          v-for="(item, index) in questionInfo.options"
          :key="index"
          :custom-style="{
            marginTop: '48rpx',
            display: 'flex',
            alignItems: 'flex-start',
          }"
          :name="item.label"
          :active-color="activeColor(item.label)"
        >
          <template #label>
            <span class="flex-1 line-height-22px" :style="{ color: labelColor(item.label) }" @click="handleClick(item.label)">{{
              `${item.label}、${item.value}`
            }}</span>
          </template>
        </up-checkbox>
      </view>
    </up-checkbox-group>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  questionInfo: {
    type: Object as () => QuestionInfo,
    required: true,
  },
});
enum Status {
  unselected, // 未选择
  selected, // 已选择
  feedback, // 答案反馈
}

interface OptionsItem {
  label: string;
  value: string;
}
interface QuestionInfo {
  status: Status;
  sequenceNum: number;
  question: string;
  answer: string | string[] | undefined;
  standardAnswer?: string | string[];
  options: Array<OptionsItem>;
}

const answer = defineModel();
const wrongColor = '#F64040';
const rightColor = '#2ECC71';
const activeColor = (optionItem: string) => {
  // 反馈阶段
  if (props.questionInfo.status === Status.feedback) {
    // 当前选项选中状态
    if (Array.isArray(answer.value) && answer.value.includes(optionItem)) {
      if (
        Array.isArray(props.questionInfo.standardAnswer)
        && props.questionInfo.standardAnswer.includes(optionItem)
      ) {
        return rightColor;
      }
      else {
        return wrongColor;
      }
    }
    // 不用修改未选中时单选框的按钮颜色
  }
};
const labelColor = (optionItem: string) => {
  // 反馈阶段
  if (props.questionInfo.status === Status.feedback) {
    // 当前选项选中状态
    if (Array.isArray(answer.value) && answer.value.includes(optionItem)) {
      // 答案正确，显示正确时的颜色
      if (
        Array.isArray(props.questionInfo.standardAnswer)
        && props.questionInfo.standardAnswer.includes(optionItem)
      ) {
        return rightColor;
      }
      // 答案错误
      else {
        return wrongColor;
      }
    }
    else {
      // 未选中状态，需要显示正确选项的文本颜色
      // 答案正确，显示正确时的颜色
      if (
        Array.isArray(props.questionInfo.standardAnswer)
        && props.questionInfo.standardAnswer.includes(optionItem)
      ) {
        return rightColor;
      }
    }
  }
};
const handleClick = (value: string) => {
  // if (answer.value === undefined) {
  //   answer.value = [];
  // }
  // else {
  //   if (Array.isArray(answer.value)) {
  //     const index = answer.value.indexOf(value);
  //     if (index !== -1) {
  //       answer.value.splice(index, 1);
  //     }
  //     else {
  //       answer.value.push(value);
  //     }
  //   }
  // }
  answer.value = value;
};
answer.value = props.questionInfo.answer;
</script>

<style lang="scss" scoped>
.quesiton-type {
  display: inline-block;
  padding-left: 16rpx;
  margin-right: 2rpx;
  width: 90rpx;
  height: 38rpx;
  font-size: 20rpx;
  background: url("/static/images/mocktest/type-bg.png") no-repeat center;
  background-size: contain;
  line-height: 38rpx;
}

.not-working {
  user-select: none;
  pointer-events: none;
}
</style>
