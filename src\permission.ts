import { getAccessToken } from '@/utils/auth';

// 登录页面
const loginPage = '/pages/common/login/index';
// 页面白名单
const whiteList = [
  '/',
  '/pages/common/login/index',
  '/pages/tab/protocol/privacyPolicy',
  '/pages/tab/protocol/userAgreement',
];

// 检查地址白名单
function checkWhite(url: string) {
  const path = url.split('?')[0];
  return whiteList.includes(path);
}

// 页面跳转验证拦截器
const list = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab'];
list.forEach((item) => {
  uni.addInterceptor(item, {
    invoke(to) {
      console.log(item, to, 9876);
      if (getAccessToken()) {
        if (to.url === loginPage)
          uni.reLaunch({ url: '/pages/tab/home/<USER>' });

        return true;
      }
      else {
        console.log('noAccessToken', checkWhite(to.url));
        if (checkWhite(to.url)) return true;

        uni.reLaunch({ url: loginPage });
        return false;
      }
    },
    fail(err) {
      console.log(err);
    },
  });
});
