<template>
  <view class="box-border h-100% flex flex-col justify-between pa-30rpx">
    <div class="flex-1">
      <view class="info rd-12rpx bg-white py-50rpx">
        <view class="mb-60rpx text-align-center text-32rpx text-#333">
          <image
            src="/static/images/mocktest/success.png"
            class="h-100rpx w-100rpx"
          />
          <view>交卷成功</view>
        </view>
        <view class="flex justify-around">
          <view>
            <view class="text-24rpx text-#999">
              得分：
            </view>
            <view class="mt-8rpx text-32rpx text-#333">
              {{ submitInfo.exercisesPaperScore }}分
            </view>
          </view>
          <view>
            <view class="text-24rpx text-#999">
              评价：
            </view>
            <view
              class="mt-8rpx"
              :class="[
                submitInfo.exercisesPaperComment == '不合格'
                  ? 'text-#FE474A'
                  : submitInfo.exercisesPaperComment == '合格'
                    ? 'text-#4076F6'
                    : 'text-#2ECC71',
              ]"
            >
              {{ submitInfo.exercisesPaperComment }}
            </view>
          </view>
        </view>
      </view>
    </div>
    <view class="brush-btn m-32rpx">
      <up-button
        type="primary"
        text="开始刷题"
        shape="circle"
        @click="back"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
const submitInfo = ref<any>({});
const back = () => {
  uni.navigateBack({
    delta: 2,
  });
};

onMounted(() => {
  const { proxy } = getCurrentInstance();
  const options = proxy.$scope.options;
  submitInfo.value = JSON.parse(options.queryInfo);
  // 去掉顶部导航返回按钮
  // const backBtn: any = document.getElementsByClassName('uni-page-head-hd')[0];
  // backBtn.style.display = 'none';
});
</script>

<style scoped lang="scss">
// .brush-btn {
//   margin-bottom: 50rpx;
//   width: 100%;
//   box-sizing: border-box;
// }
.info {
  box-shadow: 0rpx 2rpx 24rpx 0rpx rgba(0, 0, 0, 0.06);
}
</style>
