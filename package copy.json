{"name": "uniapp-vue3-project", "type": "module", "version": "1.0.9", "license": "MIT", "scripts": {"dev:h5": "uni", "dev:h5-pro": "uni --mode production", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-weixin-prod": "uni -p mp-weixin --mode production", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "build:h5": "uni build", "build:h5-prod": "uni build  --mode production", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-weixin-prod": "uni build -p mp-weixin --mode production", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "type-check": "vue-tsc --noEmit", "eslint": "eslint \"src/**/*.{js,jsx,ts,tsx,vue}\" --fix", "stylelint": "stylelint \"src/**/*.(vue|scss|css)\" --fix", "cz": "git add . && npx czg", "postinstall": "simple-git-hooks"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-app-harmony": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-app-plus": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-components": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-h5": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-mp-alipay": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-mp-baidu": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-mp-jd": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-mp-kuaishou": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-mp-lark": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-mp-qq": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-mp-toutiao": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-mp-xhs": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-quickapp-webview": "3.0.0-alpha-4020820240920001", "pinia": "2.0.36", "uview-plus": "^3.3.27", "vue": "3.4.21", "vue-i18n": "^9.14.0", "z-paging": "^2.7.11"}, "devDependencies": {"@antfu/eslint-config": "2.21.1", "@dcloudio/types": "^3.4.12", "@dcloudio/uni-automator": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-cli-shared": "3.0.0-alpha-4020820240920001", "@dcloudio/uni-stacktracey": "3.0.0-alpha-4020820240920001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-4020820240920001", "@iconify-json/mdi": "^1.1.68", "@types/node": "^20.16.1", "@typescript-eslint/parser": "^7.15.0", "@uni-helper/uni-app-types": "0.5.13", "@unocss/eslint-plugin": "^0.61.0", "@unocss/preset-icons": "^0.61.0", "@vue/runtime-core": "^3.4.30", "@vue/tsconfig": "^0.5.1", "czg": "^1.9.4", "eslint": "^8.57.0", "lint-staged": "^15.2.5", "miniprogram-api-typings": "^3.12.2", "picocolors": "^1.0.1", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.8", "simple-git-hooks": "^2.11.1", "stylelint": "^16.6.1", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-order": "^6.0.3", "typescript": "^5.4.5", "unocss": "^0.61.0", "unocss-applet": "^0.8.2", "unplugin-auto-import": "^0.18.0", "unplugin-vue-components": "^0.27.2", "vite": "^5.2.8", "vite-plugin-clean-build": "^1.2.1", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-replace-image-url": "^1.2.1", "vue-tsc": "^2.0.26"}, "simple-git-hooks": {"pre-commit": "npx lint-staged", "commit-msg": "node scripts/verifyCommit.js"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": "eslint --fix", "*.{scss,less,style,html}": "stylelint --fix", "*.vue": ["eslint --fix", "stylelint --fix"]}}