<template></template>

<script lang="ts" setup>
import { ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form/index';
import { formSchema } from './batchAdd.data';
const emit = defineEmits(['register', 'success']);
const isUpdate = ref(true);
const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
