const TokenKey = 'admin-token'
const AccessTokenKey = 'ACCESS_TOKEN'
const RefreshTokenKey = 'REFRESH_TOKEN'
const TokenPrefix = 'Bearer '
function isLogin() {
    return !!uni.getStorageSync(TokenKey)
}
function getToken() {
    return uni.getStorageSync(TokenKey)
}
function getAccessToken() {
    return uni.getStorageSync(AccessTokenKey)
}
function getRefreshToken() {
    return uni.getStorageSync(RefreshTokenKey)
}
function setToken(token: any) {
    uni.setStorageSync('userInfo', token)
    uni.setStorageSync(RefreshTokenKey, token.refreshToken)
    uni.setStorageSync(AccessTokenKey, token.accessToken)
}
function clearToken() {
    uni.removeStorageSync(RefreshTokenKey)
    uni.removeStorageSync(AccessTokenKey)
    uni.removeStorageSync('tenantId')
}
function timestampToStandardTime(timestamp) {
    var date = new Date(timestamp);
    var Y = date.getFullYear() + "-";
    var M =
      (date.getMonth() + 1 < 10
        ? "0" + (date.getMonth() + 1)
        : date.getMonth() + 1) + "-";
    var D = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
    var h = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
    var m = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
    var s = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
    return Y + M + D + " " + h + ":" + m + ":" + s;
  };
export { TokenPrefix, isLogin, getAccessToken, setToken, clearToken, getRefreshToken, getToken,timestampToStandardTime }
