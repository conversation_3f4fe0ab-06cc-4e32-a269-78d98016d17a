<template>
  <view class="exam-box h-full">
    <view class="bg-white py-30rpx rd-12rpx info ma-30rpx">
      <view class="text-align-center mb-40rpx text-#333 text-32rpx">{{
        paperData.paperName
      }}</view>
      <view class="flex justify-around">
        <view v-for="(item, index) in mainInfoList" :key="index" class="flex-col items-center">
          <view class="text-#333 text-32rpx text-align-center">
            {{ paperData[item.field] }}
          </view>
          <view class="text-#999 text-24rpx mt-8rpx">{{ item.title }}</view>
        </view>
      </view>
    </view>
    <view class="detail-box mt-20rpx px-30rpx">
      <view class="text-#333 text-32rpx font-700">模拟记录</view>
      <view class="list-wrap">
        <view class="bg-white text-#333 text-24rpx record-box rd-12rpx pa-30rpx my-20rpx relative" v-for="(item, index) in recordList" :key="index" @click="goTestDetail(item)">
          <view class="mb-40rpx">
            <text>开始时间：{{
                timestampToStandardTime(item.exercisesExamStartTime)
              }}</text>
            <text class="ml-40rpx">用时：{{
                convertSecondsToMinutesAndSeconds(item.exercisesExamUseTime)
              }}</text>
          </view>
          <view>
            <text>交卷时间：{{
                timestampToStandardTime(item.exercisesExamEndTime)
              }}</text>
            <text class="ml-40rpx">得分：<text :class="fontColorMap[item.exercisesPaperComment]">{{
                item.exercisesPaperScore
              }}</text></text>
          </view>
          <image :src="imgSrcMap[item.exercisesPaperComment]" class="w-74rpx h-65rpx absolute level-img"></image>
        </view>

        <uni-load-more v-if="total > 0" :status="loadStatus" :content-text="{
          contentdown: '上拉加载更多',
        }" @tap="loadMore" />
        <no-data v-if='!!!recordList.length'></no-data>
      </view>
    </view>
    <view v-if="goTextBtn" class="brush-btn pa-32rpx">
      <up-button type="primary" text="开始模拟" shape="circle" @click="goTest"></up-button>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { MockTestApi } from "@/api";
  import { timestampToStandardTime } from "@/utils";
  const paperId = ref();
  const exercisesPlanId = ref();
  const paperData = ref<any>({});
  const pageNo = ref<number>(1);
  const pageSize = ref<number>(10);
  const total = ref<number>(0);
  const loadStatus = ref('');
  const paperItemPointInfo = ref<any>({});
  const goTextBtn = ref<any>(false)
  const mainInfoList = ref<any>([
    {
      title: "试题量",
      field: "questionTotal",
    },
    {
      title: "试卷总分",
      field: "paperTotalScore",
    },
    {
      title: "建议用时",
      field: "paperTime",
    },
  ]);
  const recordList = ref<any>([]);
  // 字体颜色映射
  const fontColorMap : any = {
    不合格: "text-#EA3323",
    合格: "text-#FF9B18",
    优秀: "text-#0984F9",
  };

  // 图片路径映射
  const imgSrcMap : any = {
    不合格: "/static/images/mocktest/nopass.png",
    合格: "/static/images/mocktest/pass.png",
    优秀: "/static/images/mocktest/excellent.png",
  };
  const getPaperMainInfo = async () => {
    const params = {
      exercisesPlanId: exercisesPlanId.value,
      paperId: paperId.value,
    };
    const res = await MockTestApi.findPaperMainInfo(params);
    paperData.value = res.data;
    paperItemPointInfo.value = res.data.paperItemPointInfo;
  };
  const getTestRecord = async () => {
    const params = {
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      exercisesPlanId: exercisesPlanId.value,
      paperId: paperId.value,
    };
    const res = await MockTestApi.getMockRecord(params);
    if (res.code !== 0) {
      return;
    }
    return res;
    // total.value = res.data.total;
    // recordList.value = [...recordList.value, ...res.data.list];
    // loadStatus.value = recordList.value.length < total.value ? 'more' : 'noMore';

  };
  //测试
  const goTest = () => {
    const queryInfo = {
      exercisesPlanId: exercisesPlanId.value,
      paperId: paperId.value,
      paperName: paperData.value.paperName,
      paperItemPointInfo: paperItemPointInfo.value,
      questionExercisesPaperStatisticsId: paperData.value.questionExercisesPaperStatisticsId,
      token: paperData.value.token,
      requestType: 5,
    }
    uni.navigateTo({
      url: `/pages/tab/exam/index?queryInfo=${JSON.stringify(queryInfo)}`,
    });
    // uni.navigateTo({
    //   url: `/pages/tab/exam/index?exercisesPlanId=${exercisesPlanId.value
    //     }&paperId=${paperId.value}&paperName=${paperData.value.paperName
    //     }&paperItemPointInfo=${JSON.stringify(paperItemPointInfo.value)}
    // &questionExercisesPaperStatisticsId=${paperData.value.questionExercisesPaperStatisticsId
    //     }&token=${paperData.value.token}`,
    // });
  };
  //测试记录详情
  const goTestDetail = (item: any) => {
    console.log(item, "item");

    const queryInfo = {
      exercisesPlanId: exercisesPlanId.value,
      paperId: paperId.value,
      paperName: paperData.value.paperName,
      paperItemPointInfo: paperItemPointInfo.value,
      questionExercisesPaperStatisticsId: item.id,
      token: paperData.value.token,
      requestType: 6,
    }
    uni.navigateTo({
      url: `/pages/tab/exam/index?queryInfo=${JSON.stringify(queryInfo)}`,
    });
    // uni.navigateTo({
    //   url: `/pages/tab/exam/index?exercisesPlanId=${exercisesPlanId.value}&paperId=${paperId.value}&paperName=${paperData.value.paperName}
    // &questionExercisesPaperStatisticsId=${item.id}&pageType=detail&sequenceNum=0`,
    // });
  };
  //秒转化为分+秒
  const convertSecondsToMinutesAndSeconds = (seconds) => {
    var minutes = Math.floor(seconds / 60);
    var remainingSeconds = seconds % 60;
    return minutes + "分" + remainingSeconds + "秒";
  };

  // 加载更多
  async function loadMore() {
    if (loadStatus.value === 'noMore') {
      return;
    }
    pageNo.value++;
    const res = await getTestRecord();
    total.value = res?.data?.total;
    recordList.value = [...recordList.value, ...res?.data.list];
    loadStatus.value = recordList.value.length < total.value ? 'more' : 'noMore';
  }

  onReachBottom(() => {
    loadMore()
  });
  onShow(async () => {
    const { proxy } = getCurrentInstance();
    const options = proxy.options || proxy.$scope.options;
    exercisesPlanId.value = options.exercisesPlanId;
    paperId.value = options.paperId;
    pageNo.value = 1;
    await getPaperMainInfo();
    const res = await getTestRecord();
    total.value = res?.data?.total;
    recordList.value = res?.data.list;
    loadStatus.value = recordList.value.length < total.value ? 'more' : 'noMore';
    goTextBtn.value = true;
  });
</script>

<style scoped lang="scss">
  .exam-box {
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .detail-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .list-wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: scroll;
  }
  .info {
    box-shadow: 0rpx 2rpx 24rpx 0rpx rgba(0, 0, 0, 0.06);
  }

  .record-box {
    box-shadow: 0rpx 2rpx 24rpx 0rpx rgba(0, 0, 0, 0.06);

    .level-img {
      right: 0;
      bottom: 0;
    }
  }

  .brush-btn {
    // position: fixed;
    margin: 50rpx 0;
    width: 100%;
    box-sizing: border-box;
  }
</style>
