<template>
  <div class="file-upload-item">
    <!-- 有文件时：显示文件信息和操作按钮 -->
    <div v-if="fileList.length > 0" class="file-actions-container">
      <!-- 文件类型图标 -->
      <span class="file-type-icon" :class="getFileTypeClass(fileList[0])">
        {{ getFileTypeText(fileList[0]) }}
      </span>

      <!-- 操作按钮 -->
      <a-space size="small">
        <!-- 预览按钮 -->
        <a-button type="link" size="small" @click="handlePreview(fileList[0])">
          预览
        </a-button>

        <!-- 编辑状态：显示删除和重新上传按钮 -->
        <template v-if="!disabled">
          <a-button type="link" size="small" @click="handleDelete(0)">
            删除
          </a-button>
          <a-upload
            :accept="acceptTypes"
            :before-upload="(file: File) => beforeUpload(file, 0)"
            :show-upload-list="false"
            :multiple="false"
          >
            <a-button type="link" size="small">
              重新上传
            </a-button>
          </a-upload>
        </template>
      </a-space>
    </div>

    <!-- 无文件时：显示上传提示或占位符 -->
    <div v-else class="upload-placeholder">
      <a-upload
        v-if="!disabled"
        :accept="acceptTypes"
        :before-upload="beforeUpload"
        :show-upload-list="false"
        :multiple="false"
      >
        
        <span class="upload-text">仅支持excel、word、pdf格式上传</span>
      </a-upload>
      <span v-else class="empty-placeholder">-</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue';
import { uploadApi } from '/@/api/sys/upload';
import { }
interface FileItem {
  name: string;
  url: string;
  uid: string;
  size?: number;
}

const props = defineProps({
  fileList: {
    type: Array as () => FileItem[],
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:fileList']);

// 支持的文件类型
const acceptTypes = '.xlsx,.xls,.doc,.docx,.pdf';

// 文件类型验证
const validateFileType = (file: File) => {
  const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
                       'application/vnd.ms-excel',
                       'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                       'application/msword',
                       'application/pdf'];
  const allowedExtensions = ['.xlsx', '.xls', '.doc', '.docx', '.pdf'];
  
  const isValidType = allowedTypes.includes(file.type);
  const isValidExtension = allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
  
  return isValidType || isValidExtension;
};

// 上传前验证
const beforeUpload = async (file: File, replaceIndex?: number) => {
  // 文件类型验证
  if (!validateFileType(file)) {
    message.error('仅支持excel、word、pdf格式的文件！');
    return false;
  }

  // 文件大小验证（限制10MB）
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB！');
    return false;
  }

  try {
    // 调用上传API
    const response = await uploadApi({
      file,
      name: 'file',
      filename: file.name
    }, (progressEvent) => {
      // 可以在这里处理上传进度
      console.log('上传进度:', Math.round((progressEvent.loaded * 100) / progressEvent.total));
    });

    if (response && response.success) {
      const newFile: FileItem = {
        name: file.name,
        url: response.message || response.data?.url || '',
        uid: Date.now().toString(),
        size: file.size
      };

      // 更新文件列表
      let newFileList: FileItem[];
      if (typeof replaceIndex === 'number') {
        // 重新上传，替换指定位置的文件
        newFileList = [...props.fileList];
        newFileList[replaceIndex] = newFile;
      } else {
        // 新上传，添加到列表
        newFileList = [...props.fileList, newFile];
      }
      emit('update:fileList', newFileList);
      
      message.success('文件上传成功！');
    } else {
      message.error('文件上传失败！');
    }
  } catch (error) {
    console.error('上传失败:', error);
    message.error('文件上传失败！');
  }

  return false; // 阻止默认上传行为
};

// 预览文件
const handlePreview = (file: FileItem) => {
  if (file.url) {
    window.open(file.url, '_blank');
  } else {
    message.warning('文件链接不存在！');
  }
};

// 删除文件
const handleDelete = (index: number) => {
  const newFileList = [...props.fileList];
  newFileList.splice(index, 1);
  emit('update:fileList', newFileList);
  message.success('文件删除成功！');
};

// 获取文件类型样式类
const getFileTypeClass = (file: FileItem) => {
  if (!file || !file.name) return 'file-type-default';

  const extension = file.name.toLowerCase().split('.').pop();
  switch (extension) {
    case 'pdf':
      return 'file-type-pdf';
    case 'doc':
    case 'docx':
      return 'file-type-word';
    case 'xls':
    case 'xlsx':
      return 'file-type-excel';
    default:
      return 'file-type-default';
  }
};

// 获取文件类型文本
const getFileTypeText = (file: FileItem) => {
  if (!file || !file.name) return 'DOC';

  const extension = file.name.toLowerCase().split('.').pop();
  switch (extension) {
    case 'pdf':
      return 'PDF';
    case 'doc':
    case 'docx':
      return 'DOC';
    case 'xls':
    case 'xlsx':
      return 'XLS';
    default:
      return 'DOC';
  }
};
</script>

<style lang="less" scoped>
.file-upload-item {
  .upload-placeholder {
    .upload-text {
      color: #666;
      font-size: 14px;
      cursor: pointer;

      &:hover {
        color: #1890ff;
      }
    }

    .empty-placeholder {
      color: #999;
      font-size: 14px;
    }
  }

  .file-actions-container {
    display: flex;
    align-items: center;
    gap: 12px;

    .file-type-icon {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 2px;
      font-size: 12px;
      font-weight: bold;
      color: white;
      min-width: 32px;
      text-align: center;

      &.file-type-pdf {
        background-color: #ff4d4f;
      }

      &.file-type-word {
        background-color: #1890ff;
      }

      &.file-type-excel {
        background-color: #52c41a;
      }

      &.file-type-default {
        background-color: #666;
      }
    }
  }
}
</style>
