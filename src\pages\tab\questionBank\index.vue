<template>
  <view class="relative h-full">
    <view class="bg-white px-60rpx py-30rpx">
      <view class="text-32rpx text-#333 font-700">
        试题量：{{ questionBankData.questionTotal }}
      </view>
      <view class="mt-30rpx flex justify-around">
        <view>
          <progress-circle
            class="mine-member-level-progress" :diameter="80" :hoop-thickness="5" hoop-color="#4076F6"
            :percent="completePercent" :animate="false"
          />
          <view class="mt-20rpx text-align-center text-28rpx text-#333 font-500">
            完成率
          </view>
        </view>
        <view class="flex-col items-center">
          <progress-circle
            class="mine-member-level-progress" :diameter="80" :hoop-thickness="5" hoop-color="#03CCA8"
            :percent="rightPercent" :animate="false"
          />
          <view class="mt-20rpx text-align-center text-28rpx text-#333 font-500">
            正确率
          </view>
        </view>
      </view>
    </view>
    <view class="my-20rpx flex justify-between bg-white px-84rpx py-30rpx">
      <view v-for="(item, index) in questionTypeData" :key="index" @click="goBrush(item.type)">
        <view class="type-box text-align-center">
          <image :src="item.src" />
        </view>
        <text class="mt-20rpx text-24rpx text-#333">
          {{ item.title }}
        </text>
      </view>
    </view>
    <!-- <tree
      v-if="treeData.length"
      :knowledge-tree-info="treeData"
      :default-checked-keys="defaultCheckedKeysValue"
      :default-expanded-keys="defaultExpandedKeysValue"
      @change="handleTreeChange"
    /> -->
    <view class="tree-wrap bg-white">
      <view class="border-b-1px border-b-#EBEBEB border-b-solid px-30rpx py-24rpx text-32rpx text-#333 font-700">
        模拟题库知识树
      </view>
      <view v-if="treeData.length" class="block overflow-auto" style="border: 1px solid transparent;">
        <da-tree
          ref="DaTreeRef" class="tree-box" :data="treeData" label-field="name" value-field="id" leaf-field="lastTreeFlag" :show-radio-icon="false"
          :show-total="true" :load-mode="true" :load-api="getQuestionTree" :default-checked-keys="defaultCheckedKeysValue"
          :default-expanded-keys="defaultExpandedKeysValue" @change="handleTreeChange"
        />
      </view>
      <no-data v-if="!treeData.length" />
    </view>
    <view class="brush-btn pa-32rpx">
      <up-button type="primary" text="开始刷题" shape="circle" @click="goBrush(4)" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { questionBankApi } from '@/api';
// import tree from '@/pages/tab/components/tree/index.vue';

const exercisesPlanId = ref(); // 计划id
const questionLibraryId = ref(); // 题库id
const careerCode = ref(); // 职业工种等级编码
const questionName = ref();
const completePercent = ref(0);
const rightPercent = ref(0);
const questionTypeData = ref<any>([
  {
    title: '我的收藏',
    src: '/static/images/question/collect.png',
    type: 1,
  },
  {
    title: '我的错题',
    src: '/static/images/question/wrong.png',
    type: 2,
  },
  {
    title: '背题模式',
    src: '/static/images/question/recite.png',
    type: 3,
  },
]);
const questionBankData = ref<any>({});
const getquestionBankDetail = async () => {
  uni.showLoading({
    title: '加载中',
  });
  const params = {
    exercisesPlanId: exercisesPlanId.value,
    questionLibraryId: questionLibraryId.value,
  };
  const res = await questionBankApi.findStatisticsDetail(params);
  if (res.code == 0) {
    questionBankData.value = res.data;
    completePercent.value = res.data.questionFinishedCountRate / 100;
    rightPercent.value = res.data.questionRightCountRate / 100;
    const initTreeParams = {
      exercisesPlanId: exercisesPlanId.value,
      questionLibraryId: questionLibraryId.value,
      careerCode: careerCode.value,
      parentId: 0,
      type: 1,
    };
    getInitQuestionTree(initTreeParams); // 初始化加载树
  }
};
const treeData = ref([]);
const DaTreeRef = ref();
// 单选时默认值为字符串或数值，不能为数组
const defaultCheckedKeysValue = ref(0);
const defaultExpandedKeysValue = ref([]); // 默认展开的节点
const getInitQuestionTree = async (params) => {
  const res = await questionBankApi.getTreeByLevel(params);
  if (res.code == 0) {
    uni.hideLoading();
    treeData.value = res.data;
    DaTreeRef.value.setExpandedKeys('all', false);
  }
};
  // 按层级查询知识树
const getQuestionTree = async (current) => {
  uni.showLoading({
    title: '加载中',
  });
  const params = {
    exercisesPlanId: exercisesPlanId.value,
    questionLibraryId: questionLibraryId.value,
    careerCode: careerCode.value,
    parentId: current.originItem.id,
    type: current.originItem.type,
  };
  const res = await questionBankApi.getTreeByLevel(params);
  if (res.code == 0) {
    uni.hideLoading();
    return res.data;
  }
};
const handleTreeChange = (values, currentItem) => {
  console.log(values, currentItem, 'questionBank');

  const item = currentItem.originItem;
  const treeInfo = currentItem.originItem.knowledgeTreePointInfo;
  // if (item.type == 3) {
  const queryInfo = {
    exercisesPlanId: exercisesPlanId.value,
    questionLibraryId: questionLibraryId.value,
    parentKnowledgeTreeId: item.parentKnowledgeTreeId,
    currentTreeId: item.id,
    requestType: treeInfo.requestType,
    sequenceNum: treeInfo.sequenceNum,
    careerCode: careerCode.value,
    libraryItemId: item.lastLibraryItemId,
    questionName: item.knowledgeName,
    lastTreeFlag: item.lastTreeFlag,
  };
  console.log(queryInfo);

  uni.navigateTo({
    url: `/pages/tab/brush/index?queryInfo=${JSON.stringify(queryInfo)}`,
  });
};

const goBrush = (requestType) => {
  let libraryItemId;
  switch (requestType) {
    case 1:
      libraryItemId = questionBankData.value.lastCollectItemId;
      break;
    case 2:
      libraryItemId = questionBankData.value.lastErrorItemId;
      break;
    case 3:
      libraryItemId = questionBankData.value.lastLearnLibraryItemId;
      break;
    case 4:
      libraryItemId = questionBankData.value.lastLibraryItemId;
      break;
  }
  const queryInfo = {
    exercisesPlanId: exercisesPlanId.value,
    questionLibraryId: questionLibraryId.value,
    requestType,
    libraryItemId,
    careerCode: careerCode.value,
    questionName: questionName.value,
  };
  uni.navigateTo({
    url: `/pages/tab/brush/index?queryInfo=${JSON.stringify(queryInfo)}`,
  });
};
onShow(() => {
  const { proxy } = getCurrentInstance();
  const options = proxy.options || proxy.$scope.options;
  exercisesPlanId.value = options.exercisesPlanId;
  questionLibraryId.value = options.questionLibraryId;
  careerCode.value = options.careerCode;
  questionName.value = options.questionName;
  uni.setNavigationBarTitle({
    title: options.questionName,
  });

  getquestionBankDetail();
});
</script>

<style scoped lang="scss">
  .relative {
    display: flex;
    overflow: hidden;
    flex-direction: column;
  }

  .type-box {
    text-align: center;

    image {
      width: 86rpx;
      height: 86rpx;
    }
  }

  .tree-wrap {
    display: flex;
    overflow: hidden;
    flex: 1;
    flex-direction: column;

    .tree-box {
      flex: 1;
      overflow: scroll;
    }
  }

  .brush-btn {
    // position: fixed;
    margin-bottom: 50rpx;
    width: 100%;
    box-sizing: border-box;
  }
</style>
