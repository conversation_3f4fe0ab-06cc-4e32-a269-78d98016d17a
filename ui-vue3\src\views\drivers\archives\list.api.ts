import { defHttp } from '/@/utils/http/axios';

export enum Api {
  list = '/sys/user/queryByOrgCodeForAddressList',
  positionList = '/sys/position/list',
  queryDepartTreeSync = '/sys/sysDepart/queryDepartTreeSync',
  downloadTemplate = '/drivers/archives/downloadTemplate',
  batchImport = '/drivers/archives/batchImport',
}
/**
 * 获取部门树列表
 */
export const queryDepartTreeSync = (params?) => defHttp.get({ url: Api.queryDepartTreeSync, params });
/**
 * 部门用户信息
 */
export const list = (params?) => defHttp.get({ url: Api.list, params });
/**
 * 职务list
 */
export const positionList = (params?) => defHttp.get({ url: Api.positionList, params });

/**
 * 下载导入模板
 */
export const downloadTemplate = () => {
  return defHttp.get({
    url: Api.downloadTemplate,
    responseType: 'blob'
  });
};

/**
 * 批量导入司机档案
 */
export const batchImport = (file: File) => {
  const formData = new FormData();
  formData.append('file', file);

  return defHttp.post({
    url: Api.batchImport,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};
