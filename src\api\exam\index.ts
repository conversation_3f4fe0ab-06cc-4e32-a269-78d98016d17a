/**
 * 模拟测试答题配置相关接口
 */
import type { nextParams, detailParams, cardParams, checkParams, submitParams, answerAllParams } from './types'
import { get, post } from '@/utils/request'
import type { CommonResult } from '@/api/common/types'

enum URL {
  findItemById = 'question/exercises-paper-item/weChatMiniProgram/findItemById', //题目明细
  findNextItem = 'question/exercises-paper-item/weChatMiniProgram/findNextItem',//获得模拟测试 -下一题/上一题
  queryAnswerCardItem = 'question/exercises-paper-item/weChatMiniProgram/queryAnswerCardItem',//获取答题卡
  checkStageSubmit = 'question/exercises-paper-item/weChatMiniProgram/checkStageSubmit',//点击下一题提交答案
  submit = 'question/exercises-paper-statistics/weChatMiniProgram/submit',//提交试卷
  checkPaperAnswerAll = 'question/exercises-paper-statistics/weChatMiniProgram/checkPaperAnswerAll',//检查模拟试卷是否全部答题(true:已全部答完-false:未全部答完)

}
export const findItemById = (params : detailParams) => get<CommonResult>({ url: URL.findItemById, params })
export const findNextItem = (params : nextParams) => get<CommonResult>({ url: URL.findNextItem, params })
export const queryAnswerCardItem = (params : cardParams) => get<CommonResult>({ url: URL.queryAnswerCardItem, params })
export const checkStageSubmit = (data : checkParams) => post<CommonResult>({ url: URL.checkStageSubmit, data })
export const submitPaper = (data : submitParams) => post<CommonResult>({ url: URL.submit, data })
export const checkPaperAnswerAll = (params : answerAllParams) => get<CommonResult>({ url: URL.checkPaperAnswerAll, params })
