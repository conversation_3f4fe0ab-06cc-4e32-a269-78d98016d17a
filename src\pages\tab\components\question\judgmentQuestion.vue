<template>
  <view>
    <span class="quesiton-type">判断题</span>
    <text class="text-32rpx text-#333">
      {{ questionInfo.sequenceNum }}、{{ questionInfo.question }}
    </text>
  </view>
  <up-radio-group
    v-model="answer"
    class="quesition-group"
    label-size="32rpx"
    label-color="#333"
    placement="column"
  >
    <view
      :class="{ 'not-working': props.questionInfo.status === Status.feedback }"
    >
      <up-radio
        v-for="(item, index) in questionInfo.options"
        :key="index"
        :custom-style="{ marginTop: '48rpx' }"
        :label="`${item.value}`"
        :name="item.label"
        :active-color="activeColor(item.label)"
        :label-color="labelColor(item.label)"
      />
    </view>
  </up-radio-group>
</template>

<script setup lang="ts">
const props = defineProps({
  questionInfo: {
    type: Object as () => QuestionInfo,
    required: true,
  },
});
enum Status {
  unselected, // 未选择
  selected, // 已选择
  feedback, // 答案反馈
}

interface OptionsItem {
  label: string;
  value: string;
}
interface QuestionInfo {
  status: Status;
  sequenceNum: number;
  question: string;
  answer: string | string[] | undefined;
  standardAnswer?: string | string[];
  options: Array<OptionsItem>;
}

// const status = ref(Status.unselected);
const answer = defineModel();
const rightColor = '#2ECC71';
const wrongColor = '#F64040';
const activeColor = (optionItem: string) => {
  // 反馈阶段
  if (props.questionInfo.status === Status.feedback) {
    // 当前选项选中状态
    if (answer.value === optionItem) {
      if (optionItem === props.questionInfo.standardAnswer) {
        return rightColor;
      }
      else {
        return wrongColor;
      }
    }
    // 不用修改未选中时单选框的按钮颜色
  }
};
const labelColor = (optionItem: string) => {
  // 反馈阶段
  if (props.questionInfo.status === Status.feedback) {
    // 当前选项选中状态
    if (answer.value === optionItem) {
      // 答案正确，显示正确时的颜色
      if (optionItem === props.questionInfo.standardAnswer) {
        return rightColor;
      }
      // 答案错误
      else {
        return wrongColor;
      }
    }
    else {
      // 未选中状态，需要显示正确选项的文本颜色
      // 答案正确，显示正确时的颜色
      if (optionItem === props.questionInfo.standardAnswer) {
        return rightColor;
      }
    }
  }
};

answer.value = props.questionInfo.answer;
</script>

<style lang="scss" scoped>
.quesition-group {
  .u-radio {
    display: flex;
    align-items: flex-start;

    :deep(uni-text.u-radio__text) {
      flex: 1;
    }
  }
}
.quesiton-type {
  display: inline-block;
  width: 90rpx;
  height: 38rpx;
  line-height: 38rpx;
  background: url("/static/images/mocktest/type-bg.png") no-repeat center;
  background-size: contain;
  font-size: 20rpx;
  padding-left: 16rpx;
  margin-right: 2rpx;
}

.not-working {
  user-select: none;
  pointer-events: none;
}
</style>
