<template>
  <view class="h-full exam-box bg-white">
    <!-- 题目部分 -->
    <view class="header flex items-center bg-white pa-30rpx">
      <up-icon name="list-dot" color="#4076F6" size="24" @click="showTree" />
      <text class="ml-8rpx text-28rpx text-#333">
        知识点:{{ paperInfo.libraryItemPointInfo?.parentKnowledgeTreeName }}
      </text>
    </view>
    <view class="content px-30rpx py-36rpx">
      <view>
        <QuestionType
          v-if="showQuestion"
          v-model="answer"
          :paper-info="paperInfo"
          :request-type="queryInfo.requestType"
        />
        <analysis
          v-if="showAnalysis"
          :paper-info="paperInfo"
          :request-type="queryInfo.requestType"
        />
      </view>
    </view>
    <FooterBar
      v-if="showFooter"
      :paper-info="paperInfo"
      :query-info="queryInfo"
      :answer="answer"
      :paper-type="paperType.brush"
      @handle-click-up="handleClickUp"
      @handle-click-down="handleClickDown"
      @handle-collect="handleCollect"
    />
  </view>
  <view
    v-if="Object.keys(paperInfo).length === 0 && queryInfo.requestType === 1"
  >
    <view class="no-empty bg-white">
      <image src="/static/images/no-wrong.png" />
    </view>
  </view>
  <view
    v-if="Object.keys(paperInfo).length === 0 && queryInfo.requestType === 2"
  >
    <view class="no-empty bg-white">
      <image src="/static/images/no-wrong.png" />
      <view>暂无错题</view>
    </view>
  </view>
  <up-popup
    :show="popupShow"
    mode="left"
    :custom-style="{ width: '300px', padding: '20px 0 40px' }"
    @close="close"
  >
    <tree
      v-if="popupShow"
      :knowledge-tree-info="knowledgeTreeInfo"
      :default-checked-keys="paperInfo.parentKnowledgeTreeId"
      :default-expanded-keys="[paperInfo.parentKnowledgeTreeId]"
      @change="handleTreeChange"
    />
  </up-popup>
</template>

<script setup lang="ts">
import { getrRouteQueryData, isEmptyObject } from '@/utils/common';
import { BrushApi } from '@/api';

import QuestionType from '@/pages/tab/components/question/index.vue';
import analysis from '@/pages/tab/components/analysis/index.vue';
import tree from '@/pages/tab/components/tree/index.vue';
import FooterBar from '@/pages/tab/components/footer/index.vue';
// 路由参数
interface QueryInfo {
  exercisesPlanId: number; // 计划id
  questionLibraryId: number; // 题库id
  requestType: number; // 请求类型 1-我的收藏,2-我的错题,3-背题模式,4-刷题模式
  careerCode: string; // 职业工种等级编码
  libraryItemId: number; // 知识树id
  questionName: string; // 知识树名称
  currentTreeId?: number; // 当前树id
  parentKnowledgeTreeId?: number; // 父级树id
  sequenceNum?: number; // 题序
  lastTreeFlag?: boolean; // 是否最后一题
}
enum paperType {
  brush,
  exam,
}
const queryInfo: Ref<QueryInfo> = ref({
  exercisesPlanId: 0, // 计划id
  questionLibraryId: 0, // 题库id
  requestType: 0, // 请求类型 1-我的收藏,2-我的错题,3-背题模式,4-刷题模式
  careerCode: '', // 职业工种等级编码
  libraryItemId: 0, // 知识树id
  questionName: '', // 知识树名称
  currentTreeId: 0, // 当前树id
  parentKnowledgeTreeId: 0, // 父级树id
  sequenceNum: 0, // 题序
  lastTreeFlag: false, // 是否最后一题
});
const paperInfo: Ref<any> = ref({}); // 试卷信息
const knowledgeTreeInfo: Ref<any> = ref([]);
const showAnalysis = computed(
  () =>
    paperInfo.value.answerStatus
    && ([20, 30].includes(paperInfo.value.answerStatus)
    || queryInfo.value.requestType === 3),
);
const showFooter = computed(() => !isEmptyObject(paperInfo.value));
const showQuestion = computed(() => !isEmptyObject(paperInfo.value));
const answer = ref('');

const BAR_TITLE: {
  [key: string]: string;
} = {
  1: '我的收藏',
  2: '我的错题',
  3: '背题模式',
  4: '刷题详情',
};

const enterNewKonwledgePoint = async (libraryItemId: number, parentKnowledgeTreeId: number, currentTreeId: number, lastTreeFlag: boolean) => {
  console.log('进入新的知识树');
  const { exercisesPlanId, questionLibraryId, requestType } = queryInfo.value;
  if (libraryItemId) {
    // 非首次答题,获取题目坐标
    const res = await BrushApi.findLibraryItemByRequestType({
      libraryItemId,
      requestType,
    });
    const { data } = await BrushApi.findNextLibraryItem({
      exercisesPlanId,
      questionLibraryId,
      requestType,
      parentKnowledgeTreeId: res.data.parentKnowledgeTreeId,
      sequenceNum: res.data.sequenceNum,
      operateType: res.data.libraryItemPointInfo.lastFlag ? 30 : 20,
    });
    paperInfo.value = data;
  }
  else {
    // 首次答题
    const { data } = await BrushApi.findNextLibraryItem({
      exercisesPlanId,
      questionLibraryId,
      requestType,
      currentTreeId: lastTreeFlag ? undefined : currentTreeId,
      sequenceNum: 0,
      operateType: 20,
      parentKnowledgeTreeId: lastTreeFlag ? currentTreeId : undefined,
    });
    paperInfo.value = data;
    uni.hideLoading();
  }
};
const handleClickUp = (data: any) => {
  paperInfo.value = data;
};
const handleClickDown = (data: any) => {
  paperInfo.value = data;
};
const handleCollect = (data: any) => {
  paperInfo.value = data;
};
const popupShow = ref(false);
const showTree = async () => {
  console.log('展示知识树');
  const { requestType } = queryInfo.value;
  const { exercisesPlanId, questionLibraryId, careerCode } = paperInfo.value;
  // 获取知识树
  uni.showLoading({
    title: '加载中',
  });
  const { data } = await BrushApi.queryAllTree({
    exercisesPlanId,
    questionLibraryId,
    careerCode,
    requestType,
  });
  knowledgeTreeInfo.value = data;
  console.log(knowledgeTreeInfo.value, 'knowledgeTreeInfo');
  uni.hideLoading();
  popupShow.value = true;
};
// 关闭知识树
const close = () => {
  console.log('关闭知识树');
  popupShow.value = false;
};
// 知识树点击
const handleTreeChange = async (value: {
  sequenceNum: number;
  currentTreeId: number;
  parentKnowledgeTreeId: number;
  operateType: number;
}) => {
  uni.showLoading({
    title: '加载中',
  });
  const { exercisesPlanId, questionLibraryId, requestType } = queryInfo.value;
  const { sequenceNum, parentKnowledgeTreeId, operateType, currentTreeId } = value;
  console.log(value, 'handleTreeChange');
  paperInfo.value = {};
  const { data } = await BrushApi.findNextLibraryItem({
    exercisesPlanId,
    questionLibraryId,
    requestType,
    currentTreeId,
    parentKnowledgeTreeId,
    sequenceNum,
    operateType,
  });
  paperInfo.value = data;
  uni.hideLoading();
  close();
};

onMounted(async () => {
  // 获取路由参数
  queryInfo.value = getrRouteQueryData();
  console.log(queryInfo.value, 'queryInfo');
  // 设置标题
  uni.setNavigationBarTitle({
    title: BAR_TITLE[queryInfo.value.requestType],
  });
  // 获取问题详情
  // 非首次答题时，获取下一题，若是最后一题则显示当前题目
  const {
    libraryItemId,
    parentKnowledgeTreeId,
    currentTreeId,
    lastTreeFlag,
  } = queryInfo.value;
  enterNewKonwledgePoint(libraryItemId, parentKnowledgeTreeId as number, currentTreeId as number, lastTreeFlag as boolean);
  onBackPress(() => {
    uni.navigateTo({
      url: `/pages/tab/questionBank/index?exercisesPlanId=${queryInfo.value.exercisesPlanId}&questionName=${queryInfo.value.questionName}&careerCode=${queryInfo.value.careerCode}&questionLibraryId=${queryInfo.value.questionLibraryId}`,
    });
    return true;
  });
});
</script>

<style scoped lang="scss">
.exam-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}
.header {
  box-shadow: 0rpx 0rpx 14rpx 0rpx rgba(0, 0, 0, 0.14);
}

.content {
  flex: 1;
  overflow: scroll;
}

.footer {
  height: 50rpx;
}

.no-empty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.not-working {
  user-select: none;
  pointer-events: none;
}
</style>
