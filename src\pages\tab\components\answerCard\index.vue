<template>
  <view class="pa-30rpx">
    <view
      v-if="!showAnswerCard"
      class="h-30vh flex items-center justify-center"
    >
      <up-loading-icon text="加载中" text-size="18" />
    </view>
    <view v-else>
      <view
        class="border-b-1px border-b-#eeeeee border-b-solid pb-30rpx text-28rpx text-#333"
      >
        共{{ answerCardData?.itemAllTotal }}道试题
      </view>
      <scroll-view scroll-y class="answer-card py-30rpx">
        <block v-for="(item, index) in order" :key="index">
          <view
            v-if="
              answerCardData?.answerCardInfo[item]
                && answerCardData?.answerCardInfo[item]?.length
            "
          >
            <view class="text-28rpx text-#666">
              <text>{{ questionType[item] }}</text>
              <text>
                （每题{{
                  answerCardData?.answerCardTotal[item]?.itemEveryScore
                }}分，共{{
                  answerCardData?.answerCardTotal[item]?.itemGroupByCodeTotal
                }}题）
              </text>
            </view>
            <view class="number-box">
              <text
                v-for="(ite, ind) in answerCardData?.answerCardInfo[item]"
                :key="ind"
                class="quesition-number"
                :class="getColorCss(ite.answerStatus)"
                @click="handleClickAnswerCard(ite.id)"
              >
                {{ ind + 1 }}
              </text>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ExamApi } from '@/api';
import { isEmptyObject } from '@/utils/common';

const props = defineProps({
  paperInfo: {
    type: Object,
    required: true,
  },
  queryInfo: {
    type: Object,
    required: true,
  },
});
const emits = defineEmits(['handleClickAnswerCard']);
enum requestType {
  test = 5,
  detail = 6,
}
const answerCardData = ref<any>({});
const showAnswerCard = computed(() => !isEmptyObject(answerCardData.value));
const questionType: {
  [key: string]: string;
} = {
  B: '单选题',
  C: '判断题',
  D: '多选题',
  E: '简答题',
  F: '论述题',
};
const order = Object.keys(questionType);
const getColorCss = (answerStatus: number) => {
  if (props.queryInfo?.requestType == requestType.test) {
    return answerStatus == 10 ? '' : 'selected';
  }
  else {
    if (answerStatus == 10) {
      return '';
    }
    else if (answerStatus == 20) {
      return 'right';
    }
    else if (answerStatus == 30) {
      return 'wrong';
    }
  }
};
const handleClickAnswerCard = async (id: number) => {
  emits('handleClickAnswerCard', id);
};
onMounted(async () => {
  const { data } = await ExamApi.queryAnswerCardItem({
    questionExercisesPaperStatisticsId:
      props.paperInfo.questionExercisesPaperStatisticsId,
    exercisesPlanId: props.paperInfo.exercisesPlanId,
    paperId: props.paperInfo.paperId,
    sequenceNum: props.paperInfo.sequenceNum,
  });
  answerCardData.value = data;
});
</script>

<style scoped lang="scss">
.answer-card {
  overflow-y: scroll;
  max-height: 60vh;

  .number-box {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20rpx 40rpx;
    place-content: center center;
    margin: 30rpx 0;

    .quesition-number {
      width: 66rpx;
      height: 66rpx;
      text-align: center;
      border: 1rpx solid #e2e2e2;
      border-radius: 50%;
      line-height: 66rpx;
    }

    .selected {
      color: #4076f6;
      background: #f5f8ff;
      border: 1rpx solid #4076f6;
    }

    .right {
      color: #2ecc71;
      background: #f5f8ff;
      border: 1rpx solid #2ecc71;
    }

    .wrong {
      color: #fe474a;
      background: rgb(254 71 74 / 8%);
      border: 1rpx solid #fe474a;
    }
  }
}
</style>
