<template>
  <view class="mb-48rpx flex items-center justify-around text-align-center">
    <up-line color="#EEEEEE" length="30%" />
    <text>试题解析</text>
    <up-line color="#EEEEEE" length="30%" />
  </view>
  <view class="left-border">
    <text>正确答案</text>
    <text class="ml-20rpx text-#2ECC71">
      {{ analysisInfoFormatter.standardAnswer?.toString() }}
    </text>
  </view>
  <!-- 背题模式隐藏 -->
  <view v-if="showAnswer" class="left-border my-48rpx">
    <text>你的答案</text>
    <text class="ml-20rpx" :class="isRight() ? 'text-#2ECC71' : 'text-#FE474A'">
      {{ analysisInfoFormatter.answer?.toString() }}
    </text>
  </view>

  <view v-if="showQuestionAnalysis" class="left-border my-48rpx">
    <text>试题解析</text>
    <view class="mt-16rpx">
      {{ analysisInfoFormatter.analysis }}
    </view>
  </view>
</template>

<script setup lang="ts">
import { watchEffect } from 'vue';

const props = defineProps({
  paperInfo: {
    type: Object,
    requred: true,
  },
  requestType: {
    type: Number,
    required: true,
  },
});
enum questionType {
  singleChoice,
  multipleChoice,
  judgment,
}

const showAnswer = ref(false);
const showQuestionAnalysis = ref(false);
const analysisInfoFormatter: Ref<any> = ref({});

const judgmentAnswer: {
  [key: string]: string;
} = {
  A: '正确',
  B: '错误',
};
const formatterQuestionType = (questionTypeCode: string) => {
  switch (questionTypeCode) {
    case 'B':
      return questionType.singleChoice;
    case 'C':
      return questionType.judgment;
    case 'D':
      return questionType.multipleChoice;
    default:
      return questionType.singleChoice;
  }
};
const formatterAnswer = (answer: string | undefined) => {
  const type = formatterQuestionType(props.paperInfo!.questionTypeCode);
  switch (type) {
    case questionType.judgment:
      return answer ? judgmentAnswer[answer] : '';
    case questionType.singleChoice:
      return answer;
    case questionType.multipleChoice:
      if (answer?.includes(',')) {
        return answer?.split(',');
      }
      else {
        return answer?.split('');
      }
    default:
      return '';
  }
};
const isRight = () => {
  const { type, standardAnswer, answer } = analysisInfoFormatter.value;
  // 多选题
  if (type === questionType.multipleChoice) {
    let answerSortArr = [];
    let standardAnswerSortArr = [];
    if (Array.isArray(answer)) {
      answerSortArr = JSON.parse(JSON.stringify(answer)).sort();
    }
    if (Array.isArray(standardAnswer)) {
      standardAnswerSortArr = JSON.parse(JSON.stringify(standardAnswer)).sort();
    }
    return answerSortArr.join() === standardAnswerSortArr.join();
  }
  // 单选题、判断题
  if (type === questionType.singleChoice || type === questionType.judgment) {
    return answer === standardAnswer;
  }
  return false;
};

watchEffect(() => {
  const paperInfo = props.paperInfo!;
  showAnswer.value = props.requestType !== 3; // 背题模式不显示我的答案
  showQuestionAnalysis.value = paperInfo.paperQuestionAnalysis; // 有试题解析时显示解析内容
  // 格式化
  analysisInfoFormatter.value = {
    type: formatterQuestionType(paperInfo.questionTypeCode),
    standardAnswer: formatterAnswer(paperInfo.paperAnswer),
    answer: formatterAnswer(paperInfo.answerExercisesQuestion),
    analysis: paperInfo.paperQuestionAnalysis,
  };
  console.log(analysisInfoFormatter.value, 'analysisInfoFormatter');
});
</script>

<style lang="scss" scoped>
.left-border {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: -30rpx;
    top: 6rpx;
    width: 8rpx;
    height: 28rpx;
    background: linear-gradient(180deg, #74aefb 0%, #4076f6 100%);
    border-radius: 0rpx 6rpx 6rpx 0rpx;
  }
}
</style>
