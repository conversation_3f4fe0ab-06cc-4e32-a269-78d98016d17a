
export interface nextParams {
    questionExercisesPaperStatisticsId:number
    exercisesPlanId: number
    paperId: number
    topicId?: number
    sequenceNum: number
    operateType: number
}
export interface detailParams {
    id: number
}
export interface cardParams {
    questionExercisesPaperStatisticsId:number
    exercisesPlanId: number
    paperId: number
    sequenceNum: number
}
export interface checkParams {
    questionExercisesPaperItemId:number
    answerExamQuestion: string
}
export interface submitParams {
    exercisesPlanId:number
    paperId: number
    id: number
    token: string
}

export interface answerAllParams {
  paperStatisticsId: number,
}
