import imageUrl from '@/static/images/logo-new.png';

const shareSuccess = () => {
  uni.showToast({
    title: '分享成功',
  });
};

const shareFail = () => {
  uni.showToast({
    title: '分享失败',
    icon: 'none',
  });
};

export default {
  data() {
    return {
      // 默认的分享参数
      share: {
        title: '职证宝',
        path: '/pages/common/login/index.vue', // 默认分享路径
        imageUrl, // 默认分享图片
        desc: '职证宝',
        content: '刷题小帮手',
      },
    };
  },
  onShareAppMessage(res) {
    console.log('全局分享', res);
    const shareInfo = res.target ? res.target.dataset.shareinfo : this.share;
    return {
      ...shareInfo,
      success(res) {
        shareSuccess();
      },
      fail(res) {
        shareFail();
      },
    };
  },
  // 分享到朋友圈
  onShareTimeline(res) {
    const shareInfo = res.target ? res.target.dataset.shareinfo : this.share;
    return {
      ...shareInfo,
      success(res) {
        shareSuccess();
      },
      fail(res) {
        shareFail();
      },
    };
  },
};
