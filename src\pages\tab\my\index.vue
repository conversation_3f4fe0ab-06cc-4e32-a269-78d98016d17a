<template>
  <view class="page-wrap flex flex-col justify-between ">
    <view>
      <view class="header">
        <view class="bg-box">
          <image src="/static/images/login-bg.png" class="background-image" />
        </view>
        <view class="info-box">
          <view class="ml-64rpx">
            <u-avatar :src="personalInfo.avatar|| '/static/images/my/touxiang.png'" size="120" />
          </view>
          <view class="personal-info ml-52rpx">
            <view class="text-44rpx text-#222 font-500">
              {{ personalInfo.name }}
            </view>
            <view class="text-24rpx text-#999 my-20rpx">
              {{ personalInfo.phone }}
            </view>
            <view class="text-24rpx text-#333 flex">
              <up-icon size="12" name="/static/images/my/tenant.png"></up-icon>
              <view class="ml-10rpx">{{ personalInfo.tenantName }}</view>
            </view>
          </view>
        </view>
      </view>
      <view class="ma-30rpx bg-white">
        <u-cell-group :border="false" :customStyle="{ borderRadius: '12rpx' }">
          <u-cell v-for="(item, index) in configList" :key="index" is-link :icon="item.icon" :title="item.configMessage"
            :name="item.configType" @click="handleConfig(item)" :titleStyle="{ marginLeft: '20rpx' }"
            :border="item.configType == 50 ? false : true">
            <template #value v-if="item.configType == 10">
              <up-number-box v-model="item.configValue" @change="valChange" inputWidth="20" buttonSize="25"
                :name="item.id"></up-number-box>
            </template>
          </u-cell>
        </u-cell-group>
      </view>
    </view>
    <view class="ma-20rpx">
      <up-button text="退出登录" plain type="error" shape="circle" @tap="handleLogout" />
    </view>
  </view>
</template>

<script setup lang="ts">
  import { MyApi } from "@/api";
  import useUserStore from "@/store/modules/user";
  const personalInfo = {
    name: uni.getStorageSync("userInfo").name,
    phone: uni.getStorageSync("userInfo").phone,
    tenantName: uni.getStorageSync("userInfo").tenantName,
    avatar: uni.getStorageSync("userInfo").avatar,
  };
  const configList = ref<any>([]);
  const configMap : any = {
    20: "清空刷题记录",
    30: "清空所有错题",
    40: "清空收藏试题",
    50: "清空缓存",
  };
  const getActivateList = async () => {
    const res = await MyApi.getActivateList();
    configList.value = res.data;
    configList.value.forEach((item : any) => {
      switch (item.configType) {
        case 10:
          item.icon = "minus-circle";
          break;
        case 20:
          item.icon = "/static/images/my/jilu.png";
          break;
        case 30:
          item.icon = "/static/images/my/cuoti.png";
          break;
        case 40:
          item.icon = "star";
          break;
        case 50:
          item.icon = "/static/images/my/huancun.png";
          break;
      }
    });
  };
  const ctsvalue = ref(0);
  // 定义方法
  const valChange = async (data) => {
    console.log(data);
    const { name, value } = data;
    const params = {
      id: name,
      configValue: value,
    };
    const res = await MyApi.touchConfig(params);
    if (res.code == 0) {
      uni.showToast({
        title: "设置成功",
        icon: "success",
        duration: 2000,
      });
    }
  };
  const handleConfig = (item) => {
    if (item.configType == 10) {
      return;
    }
    uni.showModal({
      title: "提示",
      content: `该操作不可撤回，是否确认${configMap[item.configType]}`,
      success: async function (res) {
        if (res.confirm) {
          const params = {
            id: item.id,
            configValue: item.configValue,
          };
          const touchRes = await MyApi.touchConfig(params);
          if (touchRes.code == 0) {
            uni.showToast({
              title: "清空成功",
              icon: "success",
              duration: 2000,
            });
          }
        }
      },
    });
  };
  const handleLogout = () => {
    uni.showModal({
      title: "提示",
      content: `确认退出登录吗？`,
      success: async function (res) {
        if (res.confirm) {
          await useUserStore().logout();
        }
      },
    });
  };
  onMounted(() => {
    getActivateList();
  });
</script>
<style lang="scss" scoped>
  .page-wrap {
    height: calc(100vw - 44px);
  }

  .header {
    position: relative;
    width: 100%;
    height: 470rpx;

    .bg-box {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;

      .background-image {
        width: 100%;
        height: 100%;
        z-index: -1;
        object-fit: cover;
      }
    }

    .info-box {
      width: 100%;
      position: absolute;
      left: 0;
      z-index: 99;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      align-items: center;
    }
  }
</style>
